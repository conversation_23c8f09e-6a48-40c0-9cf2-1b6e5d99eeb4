<?php

use App\Http\Controllers\Admin\BannerController;
use App\Http\Controllers\Admin\BannerItemController;
use App\Http\Controllers\Admin\BlogCategoryController;
use App\Http\Controllers\Admin\BlogController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\PageController;
use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\CommonController;
use App\Http\Controllers\CustomerController;

use App\Http\Controllers\VendorEoiController;

use App\Http\Controllers\SupportCategoryController;
use App\Http\Controllers\SupportTopicController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\SupportTicketMessageController;
use App\Http\Controllers\ProductClassController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ProductVariantController;
use App\Http\Controllers\ProductAttributeController;
use App\Http\Controllers\ProductMediaController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\SupportReasonController;
use App\Http\Controllers\DropdownController;
use App\Http\Controllers\HomeController;

Route::middleware('auth:api')->group(function () {
    Route::prefix('admin')->middleware('role:admin')->group(function () {
        Route::apiResource('product-variants', ProductVariantController::class);
        Route::apiResource('product-attributes', ProductAttributeController::class);
        Route::apiResource('product-media', ProductMediaController::class);
        Route::apiResource('products', ProductController::class);
        Route::apiResource('warehouses', WarehouseController::class);
        Route::apiResource('support-categories', SupportCategoryController::class);
        Route::apiResource('support-topics', SupportTopicController::class);
        Route::apiResource('support-reasons', SupportReasonController::class);
        Route::apiResource('support-tickets', SupportTicketController::class);
        Route::apiResource('support-ticket-messages', SupportTicketMessageController::class);
        Route::apiResource('banners', BannerController::class);
        Route::apiResource('banner-items', BannerItemController::class);
        Route::apiResource('customers', CustomerController::class);
        Route::apiResource('brands', BrandController::class);
        Route::apiResource('categories', CategoryController::class);
        Route::apiResource('product-classes', ProductClassController::class);
        Route::apiResource('blogs', BlogController::class);
        Route::apiResource('blog-categories', BlogCategoryController::class);
        Route::apiResource('pages', PageController::class);
        Route::apiResource('dropdowns', DropdownController::class);
        Route::apiResource('permissions', PermissionController::class);
        Route::apiResource('roles', RoleController::class);
        Route::apiResource('users', UserController::class);
        Route::get('permissions-list', [PermissionController::class, 'permissionList']);
        Route::post('product-media/store-update', [ProductMediaController::class, 'storeUpdate']);
    });

    Route::prefix('general')->middleware('role:admin')->group(function () {
        Route::get('blog-categories/active-list', [BlogCategoryController::class, 'categoryListByActive']);
        Route::get('categories/active-list', [CategoryController::class, 'categoryListByActive']);
        Route::get('sub-categories/by-category/{category}', [CategoryController::class, 'subCategoryList']);
        Route::put('categories/status-update/{category}', [CategoryController::class, 'statusUpdate']);
        Route::get('classes/by-category/{category}', [ProductClassController::class, 'getClassByCategory']);
        Route::get('classes/active-list', [ProductClassController::class, 'classListByActive']);
        Route::get('sub-classes/by-class/{class}', [ProductClassController::class, 'getSubClasses']);
        Route::put('classes/status-update/{class}', [ProductClassController::class, 'statusUpdate']);
        Route::get('brands/active-list', [BrandController::class, 'brandListByActive']);
        Route::put('brands/status-update/{brand}', [BrandController::class, 'status']);
        Route::put('users/ban-unban/{user}', [UserController::class, 'userBanUnBan']);

        Route::put('warehouses/status-update/{warehouse}', [WarehouseController::class, 'statusUpdate']);
        Route::get('warehouses/active-list', [WarehouseController::class, 'warehouseListByActive']);

        //Vendor EOI Routes
        Route::apiResource('vendor-eoi', VendorEoiController::class);

        // Approve EOI
        Route::post('vendor-eoi/approve', [VendorEoiController::class, 'approveEOI']);
    });

    Route::post('file-upload', [CommonController::class, 'fileUpload']);
    Route::post('multiple-file-upload', [CommonController::class, 'multipleFileUpload']);
});

// Public Home Page APIs (no authentication required)
Route::prefix('home')->group(function () {
    Route::get('/', [HomeController::class, 'index']);
    Route::get('featured-products', [HomeController::class, 'featuredProducts']);
    Route::get('latest-products', [HomeController::class, 'latestProducts']);
    Route::get('categories', [HomeController::class, 'categories']);
    Route::get('banners', [HomeController::class, 'banners']);
    Route::get('best-sellers', [HomeController::class, 'bestSellers']);
    Route::get('recommendations', [HomeController::class, 'recommendations']);
    Route::get('popular-brands', [HomeController::class, 'popularBrands']);
});

Route::middleware('throttle:public-submissions')->group(function () {
    Route::post('vendor-eoi/submit', [VendorEoiController::class, 'store']);
});
