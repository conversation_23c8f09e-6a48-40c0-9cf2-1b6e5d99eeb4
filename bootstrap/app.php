<?php

use App\Exceptions\ErrorMessageException;
use App\Http\Middleware\RoleCheckMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Routing\Exceptions\InvalidSignatureException;
use Illuminate\Support\Facades\Route;
use Illuminate\Validation\ValidationException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
        then: function ($router) {
            Route::prefix('api')
                ->middleware('api')
                ->name('api.')
                ->group(base_path('routes/auth.php'));
            Route::prefix('api')
                ->middleware('api')
                ->name('api.')
                ->group(base_path('routes/open.php'));
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'role' => RoleCheckMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (AuthenticationException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'message' => $e->getMessage(),
                ], 401);
            }
        });


        // Handling AuthenticationException
        $exceptions->render(function (AuthenticationException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'errors' => $e->getMessage(),
                    'message' => 'Unauthorized access. Please check your credentials.',
                    'status' => false,
                ], 401);
            }
        });

        // Handling ValidationException
        $exceptions->render(function (ValidationException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'errors' => $e->errors(),
                    'message' => 'Error: ' . $e->validator->errors()->first(),
                    'status' => false,
                ], 422);
            }
        });

        // Handling ModelNotFoundException
        $exceptions->render(function (ModelNotFoundException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'errors' => null,
                    'message' => 'No query results for model [' . $e->getModel() . '] with IDs [' . implode(', ', $e->getIds()) . ']',
                    'status' => false,
                ], 404);
            }
        });

        // Handling InvalidSignatureException
        $exceptions->render(function (InvalidSignatureException $e, Request $request) {
            if ($request->is('api/*')) {
                return response()->json([
                    'errors' => null,
                    'message' => 'Invalid signature or the link has expired.',
                    'status' => false,
                ], 403);
            }
        });

        // Handling custom ErrorMessageException
        $exceptions->render(function (ErrorMessageException $e, Request $request) {

            if ($request->is('api/*')) {
                return response()->json([
                    'errors' => null,
                    'message' => $e->getMessage(),
                    'status' => false,
                ], 400);
            }
        });
    })->create();
