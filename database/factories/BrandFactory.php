<?php

namespace Database\Factories;

use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class BrandFactory extends Factory
{
    protected $model = Brand::class;

    public function definition(): array
    {
        $nameEn = $this->faker->company();

        return [
            'name_en' => $nameEn,
            'name_ar' => $this->faker->optional()->company(),
            'slug' => Str::slug($nameEn) . '-' . $this->faker->unique()->numberBetween(1, 1000),
            'country_of_origin' => $this->faker->optional()->country(),
            'is_trademark_registered' => $this->faker->boolean(30),
            'website' => $this->faker->optional()->url(),
            'instagram' => $this->faker->optional()->userName(),
            'facebook' => $this->faker->optional()->userName(),

            'manufacturer' => $this->faker->boolean(20),
            'brand_owner' => $this->faker->boolean(40),
            'marketing_auth_holder' => $this->faker->boolean(15),
            'exclusive_distributor' => $this->faker->boolean(25),
            'authorized_distributor' => $this->faker->boolean(35),
            'wholesaler' => $this->faker->boolean(30),
            'authorized_retailer' => $this->faker->boolean(45),
            'direct_importer' => $this->faker->boolean(20),
            'parallel_importer' => $this->faker->boolean(10),
            'drop_shipper' => $this->faker->boolean(15),

            'skus_on_brand_website' => $this->faker->optional()->numberBetween(0, 500),
            'skus_on_amazon' => $this->faker->optional()->numberBetween(0, 200),
            'skus_on_noon' => $this->faker->optional()->numberBetween(0, 150),
            'skus_on_other_marketplaces' => $this->faker->optional()->numberBetween(0, 100),
            'skus_on_own_website' => $this->faker->optional()->numberBetween(0, 300),

            'sold_in_hypermarkets' => json_encode($this->faker->optional()->words($this->faker->numberBetween(1, 3))),
            'sold_in_pharmacies' => json_encode($this->faker->optional()->words($this->faker->numberBetween(1, 2))),
            'sold_in_specialty_stores' => json_encode($this->faker->optional()->words($this->faker->numberBetween(1, 2))),

            'mohap_registration' => $this->faker->randomElement(['yes', 'no', 'not_required']),
            'dubai_municipality_registration' => $this->faker->randomElement(['yes', 'no', 'not_required']),

            'brand_usp' => $this->faker->optional()->sentence(10),
            'top_products' => json_encode($this->faker->optional()->randomElements([
                'Product A', 'Product B', 'Product C', 'Product D', 'Product E'
            ], $this->faker->numberBetween(1, 3))),

            'logo' => $this->faker->optional()->imageUrl(200, 200, 'business'),
            'trademark_document' => $this->faker->optional()->filePath(),
            'relationship_proof' => $this->faker->optional()->filePath(),
            'purchase_proof' => $this->faker->optional()->filePath(),
            'self_declaration' => $this->faker->optional()->filePath(),
            'product_pictures' => json_encode($this->faker->optional()->randomElements([
                $this->faker->imageUrl(400, 400, 'products'),
                $this->faker->imageUrl(400, 400, 'products'),
                $this->faker->imageUrl(400, 400, 'products'),
            ], $this->faker->numberBetween(1, 3))),

            'is_active' => $this->faker->boolean(80),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'status' => 'approved',
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rejected',
            'is_active' => false,
        ]);
    }
}
