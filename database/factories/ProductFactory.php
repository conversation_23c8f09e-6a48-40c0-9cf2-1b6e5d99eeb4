<?php

namespace Database\Factories;

use App\Models\Product;
use App\Models\User;
use App\Models\Vendor;
use App\Models\Category;
use App\Models\ProductClass;
use App\Models\Brand;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class ProductFactory extends Factory
{
    protected $model = Product::class;

    public function definition(): array
    {
        $categories = Category::where('type', 'main')->get();
        $category = $this->faker->randomElement($categories);
        $subCategories = Category::where('parent_id', $category->id)->get();
        $subCategory = $subCategories->isNotEmpty() ? $this->faker->randomElement($subCategories) : null;
        
        $vendors = Vendor::all();
        $vendor = $vendors->isNotEmpty() ? $this->faker->randomElement($vendors) : null;
        
        $users = User::role('vendor')->get();
        $user = $users->isNotEmpty() ? $this->faker->randomElement($users) : User::first();
        
        $brands = Brand::where('status', 'approved')->get();
        $brand = $brands->isNotEmpty() ? $this->faker->randomElement($brands) : null;

        $titleEn = $this->faker->words(3, true);
        $regularPrice = $this->faker->randomFloat(2, 20, 500);
        $offerPrice = $regularPrice * $this->faker->randomFloat(2, 0.7, 0.95);

        return [
            'user_id' => $user->id,
            'vendor_id' => $vendor?->id,
            'category_id' => $category->id,
            'sub_category_id' => $subCategory?->id,
            'brand_id' => $brand?->id,
            'vendor_sku' => 'VND-' . $this->faker->unique()->numerify('####'),
            'barcode' => $this->faker->unique()->numerify('#############'),
            'model_number' => $this->faker->optional()->bothify('MOD-###??'),
            'title_en' => ucwords($titleEn),
            'title_ar' => $this->faker->optional()->sentence(3),
            'short_name' => $this->faker->words(2, true),
            'short_description_en' => $this->faker->sentence(10),
            'short_description_ar' => $this->faker->optional()->sentence(10),
            'description_en' => $this->faker->paragraph(3),
            'description_ar' => $this->faker->optional()->paragraph(2),
            'key_ingredients' => $this->faker->optional()->words(5, true),
            'usage_instructions' => $this->faker->optional()->sentence(8),
            'user_group' => $this->faker->optional()->randomElement(['adults', 'children', 'seniors', 'all']),
            'net_weight' => $this->faker->numberBetween(10, 2000),
            'net_weight_unit' => $this->faker->randomElement(['g', 'ml', 'capsules', 'tablets', 'pieces']),
            'formulation' => $this->faker->optional()->randomElement(['powder', 'liquid', 'capsule', 'tablet', 'cream']),
            'servings' => $this->faker->optional()->numberBetween(10, 120),
            'flavour' => $this->faker->optional()->randomElement(['vanilla', 'chocolate', 'strawberry', 'unflavored', 'mixed berry']),
            'is_variant' => $this->faker->boolean(20),
            'regular_price' => $regularPrice,
            'offer_price' => $offerPrice,
            'vat_tax' => $this->faker->randomElement([0.00, 5.00]),
            'discount_start_date' => $this->faker->optional()->dateTimeBetween('now', '+1 month'),
            'discount_end_date' => $this->faker->optional()->dateTimeBetween('+1 month', '+3 months'),
            'approx_commission' => $this->faker->randomFloat(2, 5, 25),
            'country_of_origin' => $this->faker->country(),
            'bbe_date' => $this->faker->dateTimeBetween('+1 year', '+5 years'),
            'regulatory_product_registration' => $this->faker->optional()->bothify('REG-####??'),
            'vat_tax_utl' => $this->faker->optional()->bothify('VAT-####'),
            'is_vegan' => $this->faker->boolean(30),
            'is_vegetarian' => $this->faker->boolean(50),
            'is_halal' => $this->faker->boolean(80),
            'allergen_info' => $this->faker->optional()->sentence(6),
            'storage_conditions' => $this->faker->optional()->sentence(8),
            'package_length' => $this->faker->randomFloat(2, 5, 30),
            'package_width' => $this->faker->randomFloat(2, 5, 25),
            'package_height' => $this->faker->randomFloat(2, 5, 35),
            'package_weight' => $this->faker->randomFloat(2, 0.05, 3),
            'is_active' => $this->faker->boolean(85),
            'is_approved' => $this->faker->boolean(75),
            'status' => $this->faker->randomElement(['active', 'inactive', 'pending']),
            'submission_status' => $this->faker->randomElement(['approved', 'pending', 'rejected']),
            'brand_code' => $this->faker->optional()->bothify('BR###'),
            'product_code' => $this->faker->optional()->bothify('PR####'),
            'size_code' => $this->faker->optional()->bothify('SZ##'),
            'dietary_needs' => $this->faker->optional()->randomElement(['gluten-free', 'sugar-free', 'low-sodium', 'organic']),
        ];
    }

    /**
     * Indicate that the product is active and approved.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'is_approved' => true,
            'status' => 'active',
            'submission_status' => 'approved',
        ]);
    }

    /**
     * Indicate that the product is pending approval.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
            'is_approved' => false,
            'status' => 'pending',
            'submission_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the product is a health supplement.
     */
    public function healthSupplement(): static
    {
        return $this->state(function (array $attributes) {
            $healthCategory = Category::where('code', 'H')->first();
            $vitaminSubcategory = Category::where('code', 'VS')->first();
            
            return [
                'category_id' => $healthCategory?->id,
                'sub_category_id' => $vitaminSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['capsules', 'tablets']),
                'servings' => $this->faker->numberBetween(30, 120),
                'is_halal' => true,
                'usage_instructions' => 'Take 1-2 capsules daily with food',
                'storage_conditions' => 'Store in cool, dry place',
            ];
        });
    }

    /**
     * Indicate that the product is a beauty product.
     */
    public function beauty(): static
    {
        return $this->state(function (array $attributes) {
            $beautyCategory = Category::where('code', 'B')->first();
            $skinCareSubcategory = Category::where('code', 'SC')->first();
            
            return [
                'category_id' => $beautyCategory?->id,
                'sub_category_id' => $skinCareSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['ml', 'g']),
                'net_weight' => $this->faker->numberBetween(15, 100),
                'is_vegan' => $this->faker->boolean(60),
                'usage_instructions' => 'Apply to clean skin as directed',
                'storage_conditions' => 'Store in cool place, avoid direct sunlight',
            ];
        });
    }

    /**
     * Indicate that the product is a food item.
     */
    public function food(): static
    {
        return $this->state(function (array $attributes) {
            $foodCategory = Category::where('code', 'F')->first();
            $healthFoodSubcategory = Category::where('code', 'HF')->first();
            
            return [
                'category_id' => $foodCategory?->id,
                'sub_category_id' => $healthFoodSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['g', 'ml', 'kg']),
                'net_weight' => $this->faker->numberBetween(100, 2000),
                'vat_tax' => 0.00, // Food items are VAT exempt in UAE
                'is_halal' => true,
                'storage_conditions' => 'Store in cool, dry place',
            ];
        });
    }

    /**
     * Indicate that the product is a sports nutrition item.
     */
    public function sportsNutrition(): static
    {
        return $this->state(function (array $attributes) {
            $sportsCategory = Category::where('code', 'S')->first();
            $proteinSubcategory = Category::where('code', 'PT')->first();
            
            return [
                'category_id' => $sportsCategory?->id,
                'sub_category_id' => $proteinSubcategory?->id,
                'net_weight_unit' => $this->faker->randomElement(['g', 'kg']),
                'net_weight' => $this->faker->numberBetween(500, 5000),
                'servings' => $this->faker->numberBetween(20, 100),
                'flavour' => $this->faker->randomElement(['vanilla', 'chocolate', 'strawberry', 'unflavored']),
                'usage_instructions' => 'Mix with water or milk as directed',
                'is_halal' => true,
            ];
        });
    }

    /**
     * Indicate that the product has a discount.
     */
    public function onSale(): static
    {
        return $this->state(function (array $attributes) {
            $regularPrice = $attributes['regular_price'] ?? $this->faker->randomFloat(2, 50, 300);
            $discountPercent = $this->faker->randomFloat(2, 0.1, 0.4); // 10-40% discount
            
            return [
                'regular_price' => $regularPrice,
                'offer_price' => $regularPrice * (1 - $discountPercent),
                'discount_start_date' => Carbon::now(),
                'discount_end_date' => Carbon::now()->addDays($this->faker->numberBetween(7, 60)),
            ];
        });
    }
}
