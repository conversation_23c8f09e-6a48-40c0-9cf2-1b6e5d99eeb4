<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_fulfillments', function (Blueprint $table) {
            $table->id();

            // Link to product
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            // Fulfillment type
            $table->enum('mode', ['fba', 'drop_ship', 'self_ship'])->nullable(); // Fulfillment By Amazon / vendor / self

            // Returnability & logistics
            $table->boolean('is_returnable')->default(true);
            $table->string('collection_point')->nullable();
            $table->integer('shipping_time')->nullable(); // in days
            $table->decimal('shipping_fee', 10, 2)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fulfillments');
    }
};
