<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Show vendor ratings publicly on store pages
     *
     * Use order_id to restrict feedback to verified purchases
     *
     * Enable moderation workflows via is_approved
     */
    public function up(): void
    {
        Schema::create('user_addresses', function (Blueprint $table) {
            $table->id();

            /**
             * Associated user
             */
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            /**
             * Address labeling (Home, Office, etc.)
             */
            $table->string('label')->nullable(); // e.g., Home, Office

            /**
             * Address fields
             */
            $table->string('address_line_1');
            $table->string('address_line_2')->nullable();
            $table->string('city');
            $table->string('state')->nullable();
            $table->string('postal_code')->nullable();
            $table->string('country');

            /**
             * Default address flag
             */
            $table->boolean('is_default')->default(false);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_addresses');
    }
};
