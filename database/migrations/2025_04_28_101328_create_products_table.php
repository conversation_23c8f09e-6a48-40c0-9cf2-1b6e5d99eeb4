<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('restrict');
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade');

            /**
             * Classification
             */
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->foreignId('sub_category_id');
            $table->foreignId('class_id')->nullable();
            $table->foreignId('sub_class_id')->nullable();
            $table->string('brand_id')->nullable();
            $table->string('vendor_sku')->unique()->nullable();
            $table->string('system_sku')->unique();  // System-generated SKU No input
            $table->string('barcode')->nullable();
            $table->string('model_number')->nullable();

            /**
             * Product Details
             */
            $table->string('title_en');
            $table->string('title_ar')->nullable();
            $table->string('short_name')->nullable();
            $table->text('short_description_en')->nullable();
            $table->text('short_description_ar')->nullable();
            $table->text('description_en')->nullable();
            $table->text('description_ar')->nullable();
            $table->text('key_ingredients')->nullable();
            $table->text('usage_instructions')->nullable();
            $table->string('user_group')->nullable();
            $table->string('net_weight')->nullable();
            $table->string('net_weight_unit')->nullable();
            $table->string('formulation')->nullable();
            $table->integer('servings')->nullable();
            $table->string('flavour')->nullable();

            /**
             * Pricing & Inventory
             */
            $table->boolean('is_variant')->default(false);
            $table->decimal('regular_price', 10, 2)->default(0);
            $table->decimal('offer_price', 10, 2)->nullable();
            $table->enum('vat_tax', ['exempted', 'standard_5', 'zero_rated'])->nullable();
            $table->timestamp('discount_start_date')->nullable();
            $table->timestamp('discount_end_date')->nullable();
            $table->decimal('approx_commission', 10, 2)->nullable();

            /**
                * Compliance & Fulfillment
             */

            $table->string('dietary_needs')->nullable();
            $table->boolean('is_vegan')->default(false);
            $table->boolean('is_vegetarian')->default(false);
            $table->boolean('is_halal')->default(false);
            $table->text('allergen_info')->nullable();
            $table->text('storage_conditions')->nullable();
            $table->string('vat_tax_utl')->nullable();
            $table->string('regulatory_product_registration')->nullable();
            $table->string('country_of_origin')->nullable();
            $table->date('bbe_date')->nullable(); // Best Before End

            $table->enum('mode', ['fba', 'drop_ship', 'self_ship'])->nullable();

            $table->string('collection_point')->nullable();
            $table->boolean('is_returnable')->default(false);
            $table->integer('shipping_time')->nullable(); // in days
            $table->decimal('shipping_fee', 10, 2)->nullable();

            $table->decimal('package_length', 8, 2)->nullable(); // cm
            $table->decimal('package_width', 8, 2)->nullable();  // cm
            $table->decimal('package_height', 8, 2)->nullable(); // cm
            $table->decimal('package_weight', 8, 2)->nullable(); // kg


            /**
             * Status & Control
             */
            $table->boolean('is_active')->default(false);
            $table->boolean('is_approved')->default(false); // Admin review status
            $table->enum('status', ['draft','pending','submitted',])->default('draft');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
