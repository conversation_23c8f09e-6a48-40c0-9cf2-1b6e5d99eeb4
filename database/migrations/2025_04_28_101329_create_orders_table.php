<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            /**
             * Buyer and vendor relationship
             */
            $table->foreignId('user_id')->constrained()->onDelete('cascade');     // buyer/customer
            $table->foreignId('vendor_id')->nullable()->constrained()->onDelete('cascade'); // optional: for vendor-specific orders

            /**
             * Order identification
             */
            $table->string('order_number')->unique(); // e.g., INV-2025-0001

            /**
             * Financials
             */
            $table->decimal('subtotal', 10, 2)->default(0);
            $table->decimal('discount_total', 10, 2)->default(0);
            $table->decimal('tax_total', 10, 2)->default(0);
            $table->decimal('shipping_fee', 10, 2)->default(0);
            $table->decimal('total', 10, 2)->default(0); // final total after all fees

            /**
             * Payment & shipping status
             */
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded'])->default('pending');
            $table->enum('fulfillment_status', ['pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned'])->default('pending');
            $table->enum('payment_method', ['cod', 'card', 'wallet', 'bank'])->nullable();

            /**
             * Delivery info
             */
            $table->string('shipping_address')->nullable();  // store full snapshot or use foreign key to user_addresses
            $table->string('shipping_city')->nullable();
            $table->string('shipping_country')->nullable();
            $table->string('shipping_postal_code')->nullable();
            $table->string('shipping_phone')->nullable();

            /**
             * Tracking and notes
             */
            $table->string('tracking_number')->nullable();
            $table->text('customer_note')->nullable();
            $table->text('admin_note')->nullable();

            /**
             * General status flags
             */
            $table->boolean('is_paid')->default(false);
            $table->boolean('is_active')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
