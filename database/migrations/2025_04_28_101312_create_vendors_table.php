<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendors', function (Blueprint $table) {
            $table->id();
            $table->string('code')->nullable();
            $table->string('name');
            $table->string('legal_name_en')->nullable();
            $table->string('legal_name_ar')->nullable();
            $table->string('display_name_en')->nullable();
            $table->string('display_name_ar')->nullable();
            $table->enum('business_type', ['importer', 'distributor', 'retailer'])->nullable();

            // Trade License Info
            $table->string('license_issuing_authority')->nullable();
            $table->date('license_issue_date')->nullable();
            $table->date('license_renewal_date')->nullable();
            $table->date('license_valid_till')->nullable();
            $table->string('license_entity_type')->nullable();
            $table->integer('number_of_partners')->nullable();
            $table->string('trade_license_copy')->nullable(); // file path

            // Bank Info
            $table->string('bank_name')->nullable();
            $table->string('bank_branch')->nullable();
            $table->string('account_holder_name')->nullable();
            $table->string('iban')->nullable();
            $table->string('iban_cert_copy')->nullable(); // file path
            $table->string('cheque_number')->nullable();

            // VAT / TAX
            $table->string('tax_registration_number')->nullable();
            $table->date('tax_issue_date')->nullable();
            $table->string('tax_name_ar')->nullable();
            $table->string('tax_name_en')->nullable();
            $table->string('vat_cert_copy')->nullable(); // file path

            // Director / Signing Authority
            $table->string('director_name_tl')->nullable();
            $table->string('director_designation_tl')->nullable();
            $table->string('director_name_passport')->nullable();
            $table->string('director_passport_number')->nullable();
            $table->string('director_emirates_id')->nullable();
            $table->date('director_emirates_id_issue')->nullable();
            $table->date('director_emirates_id_expiry')->nullable();
            $table->string('director_email')->nullable();
            $table->string('director_mobile')->nullable();
            $table->enum('preferred_language', ['eng', 'ar', 'any'])->nullable();
            $table->string('director_documents')->nullable(); // file path (passport + EID)

            // SPOC
            $table->string('spoc_name')->nullable();
            $table->string('spoc_designation')->nullable();
            $table->string('spoc_email')->nullable();
            $table->string('spoc_mobile')->nullable();
            $table->string('spoc_passport_number')->nullable();
            $table->string('spoc_emirates_id')->nullable();
            $table->date('spoc_emirates_id_issue')->nullable();
            $table->date('spoc_emirates_id_expiry')->nullable();
            $table->string('loa_validity')->nullable();
            $table->string('spoc_documents')->nullable(); // file path (passport + EID + LOA)

            $table->string('self_declaration_signed_by')->nullable(); // Full Name & Designation

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendors');
    }
};
