<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();

            /**
             * Link to users table (used for login/auth)
             */
            $table->foreignId('user_id')->unique()->constrained()->onDelete('cascade');

            /**
             * Extended customer profile fields
             */
            $table->string('gender')->nullable();                // Optional
            $table->date('date_of_birth')->nullable();           // Optional
            $table->integer('loyalty_points')->default(0);       // Reward program
            $table->string('customer_type')->nullable();         // e.g., 'retail', 'wholesale'
            $table->string('preferred_language', 5)->nullable(); // e.g., 'en', 'ar'
            $table->string('preferred_currency')->nullable();    // e.g., 'AED', 'USD'

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
