-<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_variants', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');

            $table->decimal('regular_price', 10, 2)->default(0);
            $table->decimal('offer_price', 10, 2)->nullable();
            $table->enum('vat_tax', ['exempted', 'standard_5', 'zero_rated'])->nullable();
            $table->timestamp('discount_start_date')->nullable();
            $table->timestamp('discount_end_date')->nullable();

            $table->integer('stock')->default(0);
            $table->string('sku')->unique()->nullable(); // Vendor-supplied
            $table->string('system_sku')->unique();      // Auto-generated
            $table->string('barcode')->nullable();

            $table->decimal('weight', 8, 2)->nullable();
            $table->decimal('length', 8, 2)->nullable();
            $table->decimal('width', 8, 2)->nullable();
            $table->decimal('height', 8, 2)->nullable();

            $table->boolean('is_active')->default(true);

            $table->index('product_id');
            $table->index('is_active');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_variants');
    }
};
