<?php

namespace Database\Seeders;

use App\Models\Dropdown;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DropdownSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $dropdowns = [
            [
                'name_en' => 'Color',
                'name_ar' => 'اللون',
                'slug' => 'color',
                'options' => [
                    ['value_en' => 'Red', 'value_ar' => 'أحمر'],
                    ['value_en' => 'Blue', 'value_ar' => 'أزرق'],
                    ['value_en' => 'Green', 'value_ar' => 'أخضر'],
                    ['value_en' => 'Yellow', 'value_ar' => 'أصفر'],
                ],
            ],
            [
                'name_en' => 'Size',
                'name_ar' => 'الحجم',
                'slug' => 'size',
                'options' => [
                    ['value_en' => 'Small', 'value_ar' => 'صغير'],
                    ['value_en' => 'Medium', 'value_ar' => 'متوسط'],
                    ['value_en' => 'Large', 'value_ar' => 'كبير'],
                    ['value_en' => 'Extra Large', 'value_ar' => 'كبير جداً'],
                ],
            ],
            [
                'name_en' => 'Material',
                'name_ar' => 'المادة',
                'slug' => 'material',
                'options' => [
                    ['value_en' => 'Cotton', 'value_ar' => 'قطن'],
                    ['value_en' => 'Polyester', 'value_ar' => 'بوليستر'],
                    ['value_en' => 'Silk', 'value_ar' => 'حرير'],
                    ['value_en' => 'Wool', 'value_ar' => 'صوف'],
                ],
            ],
        ];

        foreach ($dropdowns as $dropdownData) {
            $dropdown = Dropdown::create([
                'name_en' => $dropdownData['name_en'],
                'name_ar' => $dropdownData['name_ar'],
                'slug' => $dropdownData['slug'],
            ]);

            foreach ($dropdownData['options'] as $optionData) {
                $dropdown->options()->create([
                    'value_en' => $optionData['value_en'],
                    'value_ar' => $optionData['value_ar'],
                ]);
            }
        }
    }
}
