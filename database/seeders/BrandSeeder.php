<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some active/approved brands
        Brand::factory()
            ->count(15)
            ->active()
            ->create();

        // Create some pending brands
        Brand::factory()
            ->count(5)
            ->pending()
            ->create();

        // Create some rejected brands
        Brand::factory()
            ->count(3)
            ->rejected()
            ->create();

        // Create some specific well-known brands for testing
        $wellKnownBrands = [
            [
                'name_en' => 'Apple',
                'name_ar' => 'أبل',
                'slug' => 'apple',
                'country_of_origin' => 'United States',
                'is_trademark_registered' => true,
                'website' => 'https://www.apple.com',
                'brand_owner' => true,
                'manufacturer' => true,
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Think Different - Premium technology products',
                'top_products' => ['iPhone', 'MacBook', 'iPad'],
            ],
            [
                'name_en' => 'Samsung',
                'name_ar' => 'سامسونغ',
                'slug' => 'samsung',
                'country_of_origin' => 'South Korea',
                'is_trademark_registered' => true,
                'website' => 'https://www.samsung.com',
                'brand_owner' => true,
                'manufacturer' => true,
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Innovation and technology leadership',
                'top_products' => ['Galaxy Phone', 'Smart TV', 'Refrigerator'],
            ],
            [
                'name_en' => 'Nike',
                'name_ar' => 'نايك',
                'slug' => 'nike',
                'country_of_origin' => 'United States',
                'is_trademark_registered' => true,
                'website' => 'https://www.nike.com',
                'brand_owner' => true,
                'authorized_retailer' => true,
                'is_active' => true,
                'status' => 'approved',
                'brand_usp' => 'Just Do It - Athletic excellence',
                'top_products' => ['Air Jordan', 'Air Max', 'Dri-FIT'],
            ],
        ];

        foreach ($wellKnownBrands as $brandData) {
            Brand::create($brandData);
        }
    }
}
