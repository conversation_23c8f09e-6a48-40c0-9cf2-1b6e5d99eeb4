<?php

namespace Database\Seeders;

use App\Models\DropdownOption;
use Illuminate\Database\Seeder;

class DropdownOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ageGroups = [
            'user_group' => [
                [
                    'value_en' => 'Man',
                    'value_ar' => 'رجل',
                    'sort_order' => 1,
                ],
                [
                    'value_en' => 'Woman',
                    'value_ar' => 'امرأة',
                    'sort_order' => 2,
                ],
                [
                    'value_en' => 'All',
                    'value_ar' => 'الجميع',
                    'sort_order' => 3,
                ],
                [
                    'value_en' => 'Kids',
                    'value_ar' => 'أطفال',
                    'sort_order' => 1,
                ],
                [
                    'value_en' => 'Teens',
                    'value_ar' => 'مراهقون',
                    'sort_order' => 2,
                ],
                [
                    'value_en' => 'Adults',
                    'value_ar' => 'بالغون',
                    'sort_order' => 3,
                ],
                [
                    'value_en' => 'Senior Adults',
                    'value_ar' => 'كبار السن',
                    'sort_order' => 4,
                ],
            ]

            ,
            'formulation_dosage_form' => [
                [
                    'value_en' => 'Tablet',
                    'value_ar' => 'قرص',
                    'sort_order' => 1,
                ],
                [
                    'value_en' => 'Capsule',
                    'value_ar' => 'كبسولة',
                    'sort_order' => 2,
                ],
                [
                    'value_en' => 'Syrup',
                    'value_ar' => 'شراب',
                    'sort_order' => 3,
                ],
                [
                    'value_en' => 'Injection',
                    'value_ar' => 'حقنة',
                    'sort_order' => 4,
                ],
                [
                    'value_en' => 'Cream',
                    'value_ar' => 'كريم',
                    'sort_order' => 5,
                ],
                [
                    'value_en' => 'Ointment',
                    'value_ar' => 'مرهم',
                    'sort_order' => 6,
                ],
                [
                    'value_en' => 'Powder',
                    'value_ar' => 'مسحوق',
                    'sort_order' => 7,
                ],
                [
                    'value_en' => 'Drops',
                    'value_ar' => 'قطرات',
                    'sort_order' => 8,
                ],
                [
                    'value_en' => 'Inhaler',
                    'value_ar' => 'جهاز استنشاق',
                    'sort_order' => 9,
                ],
                [
                    'value_en' => 'Patch',
                    'value_ar' => 'لصقة',
                    'sort_order' => 10,
                ],
                [
                    'value_en' => 'Gel',
                    'value_ar' => 'جل',
                    'sort_order' => 11,
                ],
                [
                    'value_en' => 'Lozenge',
                    'value_ar' => 'أقراص مص',
                    'sort_order' => 12,
                ],
                [
                    'value_en' => 'Suppository',
                    'value_ar' => 'تحاميل',
                    'sort_order' => 13,
                ],
                [
                    'value_en' => 'Liquid',
                    'value_ar' => 'سائل',
                    'sort_order' => 14,
                ],
                [
                    'value_en' => 'Other',
                    'value_ar' => 'أخرى',
                    'sort_order' => 15,
                ],

            ],

        ];

        $optionsToInsert = [];

        foreach ($ageGroups as $key => $options) {
            foreach ($options as $option) {
                $optionsToInsert[] = array_merge(
                    ['key' => $key],
                    $option,
                    [
                        'created_at' => now(),
                        'updated_at' => now(),
                        'status' => 'active',

                    ]
                );
            }
        }

        DropdownOption::insert($optionsToInsert);
    }
}
