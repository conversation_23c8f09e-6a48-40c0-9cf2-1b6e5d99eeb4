<?php

namespace Database\Seeders;

use App\Models\Vendor;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vendors = [
            [
                'code' => 'VND001',
                'name' => 'HealthPlus UAE',
                'legal_name_en' => 'HealthPlus Trading LLC',
                'legal_name_ar' => 'هيلث بلس للتجارة ذ.م.م',
                'display_name_en' => 'HealthPlus',
                'display_name_ar' => 'هيلث بلس',
                'business_type' => 'distributor',
                'license_issuing_authority' => 'Dubai Economic Department',
                'license_issue_date' => Carbon::now()->subYears(3),
                'license_renewal_date' => Carbon::now()->subYear(),
                'license_valid_till' => Carbon::now()->addYears(2),
                'license_entity_type' => 'Limited Liability Company',
                'number_of_partners' => 2,
                'bank_name' => 'Emirates NBD',
                'bank_branch' => 'Dubai Mall Branch',
                'account_holder_name' => 'HealthPlus Trading LLC',
                'iban' => '***********************',
                'tax_registration_number' => '***************',
                'tax_issue_date' => Carbon::now()->subYears(3),
                'tax_name_en' => 'HealthPlus Trading LLC',
                'tax_name_ar' => 'هيلث بلس للتجارة ذ.م.م',
                'director_name_tl' => 'Ahmed Al Mansouri',
                'director_designation_tl' => 'Managing Director',
                'director_name_passport' => 'Ahmed Mohammed Al Mansouri',
                'director_passport_number' => '********',
                'director_emirates_id' => '784-1985-1234567-8',
                'director_emirates_id_issue' => Carbon::now()->subYears(5),
                'director_emirates_id_expiry' => Carbon::now()->addYears(5),
                'director_email' => '<EMAIL>',
                'director_mobile' => '+************',
                'preferred_language' => 'eng',
                'spoc_name' => 'Sarah Al Mansouri',
                'spoc_designation' => 'Operations Manager',
                'spoc_email' => '<EMAIL>',
                'spoc_mobile' => '+************',
                'spoc_passport_number' => 'A7654321',
                'spoc_emirates_id' => '784-1990-7654321-2',
                'spoc_emirates_id_issue' => Carbon::now()->subYears(3),
                'spoc_emirates_id_expiry' => Carbon::now()->addYears(7),
                'self_declaration_signed_by' => 'Ahmed Al Mansouri',
            ],
            [
                'code' => 'VND002',
                'name' => 'Beauty Emirates',
                'legal_name_en' => 'Beauty Emirates Trading LLC',
                'legal_name_ar' => 'بيوتي إمارات للتجارة ذ.م.م',
                'display_name_en' => 'Beauty Emirates',
                'display_name_ar' => 'بيوتي إمارات',
                'business_type' => 'retailer',
                'license_issuing_authority' => 'Abu Dhabi Economic Department',
                'license_issue_date' => Carbon::now()->subYears(2),
                'license_renewal_date' => Carbon::now()->subMonths(6),
                'license_valid_till' => Carbon::now()->addYears(3),
                'license_entity_type' => 'Limited Liability Company',
                'number_of_partners' => 1,
                'bank_name' => 'ADCB',
                'bank_branch' => 'Marina Mall Branch',
                'account_holder_name' => 'Beauty Emirates Trading LLC',
                'iban' => '***********************',
                'tax_registration_number' => '***************',
                'tax_issue_date' => Carbon::now()->subYears(2),
                'tax_name_en' => 'Beauty Emirates Trading LLC',
                'tax_name_ar' => 'بيوتي إمارات للتجارة ذ.م.م',
                'director_name_tl' => 'Fatima Al Zahra',
                'director_designation_tl' => 'CEO',
                'director_name_passport' => 'Fatima Mohammed Al Zahra',
                'director_passport_number' => '********',
                'director_emirates_id' => '784-1988-2345678-9',
                'director_emirates_id_issue' => Carbon::now()->subYears(4),
                'director_emirates_id_expiry' => Carbon::now()->addYears(6),
                'director_email' => '<EMAIL>',
                'director_mobile' => '+************',
                'preferred_language' => 'ar',
                'spoc_name' => 'Layla Hassan',
                'spoc_designation' => 'Sales Manager',
                'spoc_email' => '<EMAIL>',
                'spoc_mobile' => '+************',
                'spoc_passport_number' => 'B8765432',
                'spoc_emirates_id' => '784-1992-8765432-1',
                'spoc_emirates_id_issue' => Carbon::now()->subYears(2),
                'spoc_emirates_id_expiry' => Carbon::now()->addYears(8),
                'self_declaration_signed_by' => 'Fatima Al Zahra',
            ],
            [
                'code' => 'VND003',
                'name' => 'Nutrition Hub',
                'legal_name_en' => 'Nutrition Hub Trading LLC',
                'legal_name_ar' => 'نيوتريشن هب للتجارة ذ.م.م',
                'display_name_en' => 'Nutrition Hub',
                'display_name_ar' => 'نيوتريشن هب',
                'business_type' => 'importer',
                'license_issuing_authority' => 'Sharjah Economic Department',
                'license_issue_date' => Carbon::now()->subYears(4),
                'license_renewal_date' => Carbon::now()->subYears(2),
                'license_valid_till' => Carbon::now()->addYears(1),
                'license_entity_type' => 'Limited Liability Company',
                'number_of_partners' => 3,
                'bank_name' => 'FAB',
                'bank_branch' => 'Sharjah City Centre Branch',
                'account_holder_name' => 'Nutrition Hub Trading LLC',
                'iban' => '***********************',
                'tax_registration_number' => '***************',
                'tax_issue_date' => Carbon::now()->subYears(4),
                'tax_name_en' => 'Nutrition Hub Trading LLC',
                'tax_name_ar' => 'نيوتريشن هب للتجارة ذ.م.م',
                'director_name_tl' => 'Mohammed Al Rashid',
                'director_designation_tl' => 'General Manager',
                'director_name_passport' => 'Mohammed Ahmed Al Rashid',
                'director_passport_number' => '********',
                'director_emirates_id' => '784-1983-3456789-0',
                'director_emirates_id_issue' => Carbon::now()->subYears(6),
                'director_emirates_id_expiry' => Carbon::now()->addYears(4),
                'director_email' => '<EMAIL>',
                'director_mobile' => '+************',
                'preferred_language' => 'eng',
                'spoc_name' => 'Omar Al Rashid',
                'spoc_designation' => 'Business Development Manager',
                'spoc_email' => '<EMAIL>',
                'spoc_mobile' => '+971501122334',
                'spoc_passport_number' => 'C9876543',
                'spoc_emirates_id' => '784-1995-9876543-3',
                'spoc_emirates_id_issue' => Carbon::now()->subYears(1),
                'spoc_emirates_id_expiry' => Carbon::now()->addYears(9),
                'self_declaration_signed_by' => 'Mohammed Al Rashid',
            ],
            [
                'code' => 'VND004',
                'name' => 'Wellness World',
                'legal_name_en' => 'Wellness World Trading LLC',
                'legal_name_ar' => 'عالم العافية للتجارة ذ.م.م',
                'display_name_en' => 'Wellness World',
                'display_name_ar' => 'عالم العافية',
                'business_type' => 'distributor',
                'license_issuing_authority' => 'Ajman Economic Department',
                'license_issue_date' => Carbon::now()->subYears(1),
                'license_renewal_date' => null,
                'license_valid_till' => Carbon::now()->addYears(4),
                'license_entity_type' => 'Limited Liability Company',
                'number_of_partners' => 2,
                'bank_name' => 'RAKBANK',
                'bank_branch' => 'Ajman Branch',
                'account_holder_name' => 'Wellness World Trading LLC',
                'iban' => '***********************',
                'tax_registration_number' => '***************',
                'tax_issue_date' => Carbon::now()->subYears(1),
                'tax_name_en' => 'Wellness World Trading LLC',
                'tax_name_ar' => 'عالم العافية للتجارة ذ.م.م',
                'director_name_tl' => 'Khalid Al Nuaimi',
                'director_designation_tl' => 'Managing Partner',
                'director_name_passport' => 'Khalid Hassan Al Nuaimi',
                'director_passport_number' => '********',
                'director_emirates_id' => '784-1987-4567890-1',
                'director_emirates_id_issue' => Carbon::now()->subYears(2),
                'director_emirates_id_expiry' => Carbon::now()->addYears(8),
                'director_email' => '<EMAIL>',
                'director_mobile' => '+************',
                'preferred_language' => 'eng',
                'spoc_name' => 'Amina Al Nuaimi',
                'spoc_designation' => 'Marketing Manager',
                'spoc_email' => '<EMAIL>',
                'spoc_mobile' => '+971503344556',
                'spoc_passport_number' => 'D0987654',
                'spoc_emirates_id' => '784-1993-0987654-4',
                'spoc_emirates_id_issue' => Carbon::now()->subMonths(8),
                'spoc_emirates_id_expiry' => Carbon::now()->addYears(9),
                'self_declaration_signed_by' => 'Khalid Al Nuaimi',
            ],
        ];

        foreach ($vendors as $vendorData) {
            Vendor::updateOrCreate(
                ['code' => $vendorData['code']],
                $vendorData
            );
        }
    }
}
