<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ProductClass;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductClassSeeder extends Seeder
{
    public function run()
    {
        DB::table('product_classes')->delete();

        $csvData = $this->parseCsvData();

        $this->createHierarchy($csvData);
    }

    protected function parseCsvData(): array
    {
        return [
            [
                'category_code' => 'H',
                'category_name' => 'VHMS',
                'subcategories' => [
                    [
                        'subcategory_code' => 'VS',
                        'subcategory_name' => 'Vitamin (VS)',
                        'product_classes' => [
                            ['class_code' => 'MV', 'class_name' => 'Multivitamins', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VA', 'class_name' => 'Vitamin A', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VB', 'class_name' => 'Vitamin B', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VC', 'class_name' => 'Vitamin C', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VD', 'class_name' => 'Vitamin D', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VE', 'class_name' => 'Vitamin E', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VK', 'class_name' => 'Vitamin K', 'subclasses' => [], 'is_popular' => false],
                        ],
                    ],
                    [
                        'subcategory_code' => 'HS',
                        'subcategory_name' => 'Herbs (HS)',
                        'product_classes' => [
                            [
                                'class_code' => 'PTH',
                                'class_name' => 'Popular Trending Herbs',
                                'subclasses' => [
                                    ['name' => 'Ashwagandha', 'is_popular' => false],
                                    ['name' => 'Turmeric', 'is_popular' => false],
                                    ['name' => 'Ginseng', 'is_popular' => false],
                                ],
                                'is_popular' => false
                            ],
                            [
                                'class_code' => 'SHSE',
                                'class_name' => 'Single Herb & Std Extract',
                                'subclasses' => [
                                    ['name' => 'Alfalfa', 'is_popular' => false],
                                    ['name' => 'Aloe Vera', 'is_popular' => true],
                                    ['name' => 'Amla', 'is_popular' => false],
                                    ['name' => 'Artichoke', 'is_popular' => false],
                                    ['name' => 'Ashwagandha', 'is_popular' => true],
                                    ['name' => 'Astragalus', 'is_popular' => false],
                                    ['name' => 'Avena Sativa', 'is_popular' => false],
                                    ['name' => 'Bacopa', 'is_popular' => false],
                                    ['name' => 'Banaba Leaf', 'is_popular' => false],
                                    ['name' => 'Berberine', 'is_popular' => true],
                                    ['name' => 'Bilberry', 'is_popular' => false],
                                    ['name' => 'Bitter Melon', 'is_popular' => false],
                                    ['name' => 'Black Cohosh', 'is_popular' => false],
                                    ['name' => 'Black Seed', 'is_popular' => false],
                                    ['name' => 'Bladderwrack', 'is_popular' => false],
                                    ['name' => 'Blueberry Extract', 'is_popular' => false],
                                    ['name' => 'Boswellia', 'is_popular' => false],
                                    ['name' => 'Burdock Root', 'is_popular' => false],
                                    ['name' => "Butcher's Broom", 'is_popular' => false],
                                    ['name' => 'Cascara Sagrada', 'is_popular' => false],
                                    ['name' => "Cat's Claw", 'is_popular' => false],
                                    ['name' => 'Catuaba', 'is_popular' => false],
                                    ['name' => 'Cayenne Pepper', 'is_popular' => false],
                                    ['name' => 'Celery Seed', 'is_popular' => false],
                                    ['name' => 'Chamomile', 'is_popular' => false],
                                    ['name' => 'Chaste Berry', 'is_popular' => false],
                                    ['name' => 'Cherry Fruit', 'is_popular' => false],
                                    ['name' => 'Cinnamon Herbs', 'is_popular' => false],
                                    ['name' => 'Clove', 'is_popular' => false],
                                    ['name' => 'Cranberry', 'is_popular' => false],
                                    ['name' => 'Damiana', 'is_popular' => false],
                                    ['name' => 'Dandelion Root', 'is_popular' => false],
                                    ['name' => "Devil's Claw", 'is_popular' => false],
                                    ['name' => 'Dong Quai', 'is_popular' => false],
                                    ['name' => 'Echinacea & Goldenseal', 'is_popular' => false],
                                    ['name' => 'Echinacea', 'is_popular' => true],
                                    ['name' => 'Elderberry Sambucus', 'is_popular' => true],
                                    ['name' => 'Eleuthero', 'is_popular' => false],
                                    ['name' => 'Eyebright', 'is_popular' => false],
                                    ['name' => 'Fenugreek', 'is_popular' => false],
                                    ['name' => 'Feverfew', 'is_popular' => false],
                                    ['name' => 'Fo Ti', 'is_popular' => false],
                                    ['name' => 'Forskolin', 'is_popular' => false],
                                    ['name' => 'Garlic', 'is_popular' => true],
                                    ['name' => 'Ginger Root', 'is_popular' => true],
                                    ['name' => 'Ginkgo Biloba', 'is_popular' => true],
                                    ['name' => 'Ginseng', 'is_popular' => true],
                                    ['name' => 'Goji Supplements', 'is_popular' => false],
                                    ['name' => 'Goldenseal', 'is_popular' => false],
                                    ['name' => 'Gotu Kola', 'is_popular' => false],
                                    ['name' => 'Green Coffee Bean Extract', 'is_popular' => false],
                                    ['name' => 'Guarana', 'is_popular' => false],
                                    ['name' => 'Guggul', 'is_popular' => false],
                                    ['name' => 'Gymnema', 'is_popular' => false],
                                    ['name' => 'Hawthorn', 'is_popular' => false],
                                    ['name' => 'Hibiscus', 'is_popular' => false],
                                    ['name' => 'Holy Basil', 'is_popular' => false],
                                    ['name' => 'Hops', 'is_popular' => false],
                                    ['name' => 'Horny Goat Weed', 'is_popular' => false],
                                    ['name' => 'Horse Chestnut', 'is_popular' => false],
                                    ['name' => 'Horsetail', 'is_popular' => false],
                                    ['name' => 'Juniper', 'is_popular' => false],
                                    ['name' => 'Lemon Balm', 'is_popular' => false],
                                    ['name' => 'Licorice Root', 'is_popular' => false],
                                    ['name' => 'Maca', 'is_popular' => true],
                                    ['name' => 'Magnolia Bark Relora', 'is_popular' => false],
                                    ['name' => 'Maqui Berry', 'is_popular' => false],
                                    ['name' => 'Marshmallow Root', 'is_popular' => false],
                                    ['name' => 'Milk Thistle', 'is_popular' => true],
                                    ['name' => 'Moringa', 'is_popular' => true],
                                    ['name' => 'Mucuna', 'is_popular' => false],
                                    ['name' => 'Muira puama', 'is_popular' => false],
                                    ['name' => 'Mullein', 'is_popular' => false],
                                    ['name' => 'Nettle', 'is_popular' => false],
                                    ['name' => 'Noni', 'is_popular' => false],
                                    ['name' => 'Olive Leaf', 'is_popular' => false],
                                    ['name' => 'Parsley', 'is_popular' => false],
                                    ['name' => 'Passion Flower', 'is_popular' => false],
                                    ['name' => "Pau D'Arco", 'is_popular' => false],
                                    ['name' => 'Peppermint', 'is_popular' => false],
                                    ['name' => 'Phyllanthus', 'is_popular' => false],
                                    ['name' => 'Psyllium Husk', 'is_popular' => false],
                                    ['name' => 'Pygeum', 'is_popular' => false],
                                    ['name' => 'Red Clover', 'is_popular' => false],
                                    ['name' => 'Red Raspberry', 'is_popular' => false],
                                    ['name' => 'Red Yeast Rice', 'is_popular' => false],
                                    ['name' => 'Rhodiola', 'is_popular' => false],
                                    ['name' => 'Rose Hips', 'is_popular' => false],
                                    ['name' => 'Rosemary', 'is_popular' => false],
                                    ['name' => 'Saffron', 'is_popular' => false],
                                    ['name' => 'Sarsaparilla Smilax', 'is_popular' => false],
                                    ['name' => 'Saw Palmetto', 'is_popular' => true],
                                    ['name' => 'Schisandra', 'is_popular' => false],
                                    ['name' => 'Senna Leaves', 'is_popular' => false],
                                    ['name' => 'Skullcap', 'is_popular' => false],
                                    ['name' => 'Slippery Elm', 'is_popular' => false],
                                    ['name' => "St. John's Wort", 'is_popular' => false],
                                    ['name' => 'Tongkat Ali', 'is_popular' => false],
                                    ['name' => 'Tribulus', 'is_popular' => false],
                                    ['name' => 'Uva Ursi', 'is_popular' => false],
                                    ['name' => 'Valerian', 'is_popular' => true],
                                    ['name' => 'White Willow Bark', 'is_popular' => false],
                                    ['name' => 'Wild Yam', 'is_popular' => false],
                                    ['name' => 'Yerba Mate', 'is_popular' => false]
                                ],
                                'is_popular' => false
                            ],
                            [
                                'class_code' => 'HFHC',
                                'class_name' => 'Herbal Formula by Health Concern',
                                'subclasses' => [],
                                'is_popular' => false
                            ],
                        ],
                    ],
                    [
                        'subcategory_code' => 'DH',
                        'subcategory_name' => 'Digestive Health (DH)',
                        'product_classes' => [
                            ['class_code' => 'DE', 'class_name' => 'Digestive Enzymes', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'PBPPP', 'class_name' => 'Pro Biotics (Incl Pre & Post)', 'subclasses' => [], 'is_popular' => false],
                        ],
                    ],
                    [
                        'subcategory_code' => 'FO',
                        'subcategory_name' => 'Fish Oil & Omegas (FO)',
                        'product_classes' => [
                            ['class_code' => 'CLO', 'class_name' => 'CLO', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'O3', 'class_name' => 'Omega 3', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'O369', 'class_name' => 'Omega 369', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'O6', 'class_name' => 'Omega 6', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'O7', 'class_name' => 'Omega 7', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'VSRC', 'class_name' => 'Veg Source', 'subclasses' => [], 'is_popular' => false],
                        ],
                    ],
                    [
                        'subcategory_code' => 'BS',
                        'subcategory_name' => 'Beauty within (BS)',
                        'product_classes' => [
                            ['class_code' => 'COL', 'class_name' => 'Collagen', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'HSN', 'class_name' => 'HSN', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'HR', 'class_name' => 'Hair', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'NL', 'class_name' => 'Nails', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'SK', 'class_name' => 'Skin', 'subclasses' => [], 'is_popular' => false],
                        ],
                    ],
                    [
                        'subcategory_code' => 'SS',
                        'subcategory_name' => 'Speciality Supplements (SS)',
                        'product_classes' => [
                            ['class_code' => 'CRB', 'class_name' => 'Clinical Research Backed', 'subclasses' => [], 'is_popular' => false],
                        ],
                    ],
                    [
                        'subcategory_code' => 'WH',
                        'subcategory_name' => "Women's Health (WH)",
                        'product_classes' => [
                            ['class_code' => 'MNP', 'class_name' => 'Menopause', 'subclasses' => [], 'is_popular' => false],
                            ['class_code' => 'MV', 'class_name' => 'MultiVitamins', 'subclasses' => [], 'is_popular' => false],
                            [
                                'class_code' => 'PMS',
                                'class_name' => 'PMS',
                                'subclasses' => [
                                    ['name' => 'Evening Primrose Oil', 'is_popular' => false]
                                ],
                                'is_popular' => false
                            ],
                        ],
                    ],
                ],
            ],
        ];
    }

    protected function createHierarchy(array $data)
    {
        foreach ($data as $categoryData) {
            $category = Category::where('code', $categoryData['category_code'])->first();

            if (!$category) {
                continue;
            }

            foreach ($categoryData['subcategories'] as $subcategoryData) {
                $subcategory = Category::where('code', $subcategoryData['subcategory_code'])
                    ->where('parent_id', $category->id)
                    ->first();

                if (!$subcategory) {
                    continue;
                }

                foreach ($subcategoryData['product_classes'] as $classData) {
                    $productClass = ProductClass::create([
                        'name' => $classData['class_name'],
                        'code' => $classData['class_code'] ?? null,
                        'category_id' => $subcategory->id,
                        'parent_id' => null,
                        'is_popular' => $classData['is_popular'] ?? false,
                    ]);

                    foreach ($classData['subclasses'] as $subclassData) {
                        // Handle both string and array formats for backward compatibility
                        if (is_array($subclassData)) {
                            $subclassName = $subclassData['name'];
                            $isPopular = $subclassData['is_popular'] ?? false;
                        } else {
                            $subclassName = $subclassData;
                            $isPopular = false;
                        }

                        ProductClass::create([
                            'name' => $subclassName,
                            'code' => null,
                            'category_id' => $subcategory->id,
                            'parent_id' => $productClass->id,
                            'is_popular' => $isPopular,
                            'status' => 'active',
                        ]);
                    }
                }
            }
        }
    }
}
