<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    public function run()
    {
        Category::query()->delete();

        $csvData = $this->parseCsvData();

        $this->createCategories($csvData);
    }

    protected function parseCsvData(): array
    {
        return [
            [
                'code' => 'H',
                'name' => 'VHMS',
                'fee_text' => '10%',
                'subcategories' => [
                    ['code' => 'VS', 'name' => 'Vitamin'],
                    ['code' => 'HS', 'name' => 'Herbs'],
                    ['code' => 'MS', 'name' => 'Mineral'],
                    ['code' => 'SS', 'name' => 'Speciality Supplements'],
                    ['code' => 'AO', 'name' => 'Antioxidants'],
                    ['code' => 'BS', 'name' => 'Beauty within'],
                    ['code' => 'CH', 'name' => 'Children\'s Health'],
                    ['code' => 'DH', 'name' => 'Digestive Health'],
                    ['code' => 'EE', 'name' => 'Energy & Endurance'],
                    ['code' => 'FO', 'name' => 'Fish Oil & Omegas'],
                    ['code' => 'SF', 'name' => 'Greens & Superfoods'],
                    ['code' => 'HH', 'name' => 'Heart Health'],
                    ['code' => 'IM', 'name' => 'Immune Support'],
                    ['code' => 'JB', 'name' => 'Joint & Bone'],
                    ['code' => 'KD', 'name' => 'Kidney Support'],
                    ['code' => 'LV', 'name' => 'Liver Support'],
                    ['code' => 'MH', 'name' => 'Men\'s Health'],
                    ['code' => 'NB', 'name' => 'Nerve & Brain Health'],
                    ['code' => 'OG', 'name' => 'Organs & Glandular'],
                    ['code' => 'WH', 'name' => 'Women\'s Health'],
                    ['code' => 'SR', 'name' => 'Sleep & Relaxation'],
                    ['code' => 'CD', 'name' => 'Cleansing & Detox'],
                    ['code' => 'DS', 'name' => 'Diabetes Support'],
                    ['code' => 'EN', 'name' => 'Ear Eye & Nose'],
                    ['code' => 'MS', 'name' => 'Misc Supps'],
                ],
            ],
            [
                'code' => 'B',
                'name' => 'Beauty',
                'fee_text' => '10 TO 15%',
                'subcategories' => [
                    ['code' => 'SC', 'name' => 'Skin Care'],
                    ['code' => 'HC', 'name' => 'Hair Care'],
                    ['code' => 'PC', 'name' => 'Personal Care'],
                    ['code' => 'KB', 'name' => 'K Beauty'],
                    ['code' => 'MC', 'name' => 'Make up & Colour'],
                    ['code' => 'FP', 'name' => 'Fragrance & Perfume'],
                    ['code' => 'BH', 'name' => 'Bath & Home'],
                    ['code' => 'TE', 'name' => 'Tools & Equip'],
                    ['code' => 'MG', 'name' => 'Male Grooming'],
                    ['code' => 'BS', 'name' => 'Beauty Supplements'],
                    ['code' => '', 'name' => 'Hair Removal'],
                    ['code' => '', 'name' => 'Hair, Skin and Nail'],
                ],
            ],
            [
                'code' => 'F',
                'name' => 'Food & Drink',
                'fee_text' => '10%',
                'subcategories' => [
                    ['code' => 'HF', 'name' => 'Health Food'],
                    ['code' => 'BV', 'name' => 'Beverages'],
                    ['code' => 'FF', 'name' => 'Free From Sp Food'],
                    ['code' => 'TC', 'name' => 'Tea & Coffee'],
                    ['code' => 'HS', 'name' => 'Honey & Spreads'],
                    ['code' => 'SK', 'name' => 'Healthy Snacks'],
                    ['code' => 'NS', 'name' => 'Nuts & Seeds'],
                    ['code' => 'OR', 'name' => 'Organic & Raw'],
                    ['code' => 'SF', 'name' => 'SuperFoods & Greens'],
                    ['code' => 'VF', 'name' => 'Vegan Food'],
                    ['code' => '', 'name' => 'Memory & Brain Support'],
                    ['code' => '', 'name' => 'Hormone Boosters'],
                    ['code' => '', 'name' => 'Betaine HCL'],
                    ['code' => '', 'name' => 'Digestive Enzymes'],
                    ['code' => '', 'name' => 'Prebiotic'],
                    ['code' => '', 'name' => 'Probiotic'],
                    ['code' => '', 'name' => 'Fiber Supplement'],
                    ['code' => '', 'name' => 'Notropic & Nerve Health'],
                    ['code' => '', 'name' => 'Free From'],
                    ['code' => '', 'name' => 'Gluten Free'],
                    ['code' => '', 'name' => 'Sugar Free'],
                    ['code' => '', 'name' => 'Nut Free'],
                    ['code' => '', 'name' => 'Fat Free'],
                    ['code' => '', 'name' => 'Lactose Free'],
                    ['code' => '', 'name' => 'GMO Free'],
                    ['code' => '', 'name' => 'Other Free From Food'],
                ],
            ],
            [
                'code' => 'S',
                'name' => 'Sports Nutrition',
                'fee_text' => '4 TO 7%',
                'subcategories' => [
                    ['code' => 'PT', 'name' => 'Protein'],
                    ['code' => 'CR', 'name' => 'Creatine'],
                    ['code' => 'PW', 'name' => 'Pre-Workout Boosters'],
                ],
            ],
            [
                'code' => 'W',
                'name' => 'Weight Management',
                'fee_text' => '10%',
                'subcategories' => [
                    ['code' => 'WL', 'name' => 'Weight Loss'],
                    ['code' => 'WG', 'name' => 'Weight Gain'],
                    ['code' => 'KT', 'name' => 'Keto'],
                ],
            ],
            [
                'code' => 'P',
                'name' => 'Para Pharmacy',
                'fee_text' => '4 TO 10%',
                'subcategories' => [
                    ['code' => 'OT', 'name' => 'OTC'],
                    ['code' => 'PA', 'name' => 'Accessories'],
                    ['code' => 'OB', 'name' => 'Ointments & Balms'],
                ],
            ],
            [
                'code' => 'E',
                'name' => 'Med Equipment',
                'fee_text' => '10%',
                'subcategories' => [
                    ['code' => 'BP', 'name' => 'BP Monitor'],
                    ['code' => 'BS', 'name' => 'Glucometer'],
                    ['code' => 'HR', 'name' => 'Heart Monitors'],
                ],
            ],
            [
                'code' => 'G',
                'name' => 'Misc Gen Items',
                'fee_text' => '4 to 10%',
                'subcategories' => [
                    ['code' => '', 'name' => 'First AID'],
                    ['code' => '', 'name' => 'Safety'],
                    ['code' => '', 'name' => 'Hygine'],
                ],
            ],
        ];
    }

    protected function createCategories(array $categoriesData): void
    {
        foreach ($categoriesData as $categoryData) {
            $category = Category::create([
                'name'            => $categoryData['name'],
                'code'            => $categoryData['code'],
                'fee_text'        => $categoryData['fee_text'],
                'slug'            => Str::slug($categoryData['name']),
                'type'            => 'main',
                'parent_id'       => null,
                'ordering_number' => $this->getOrderingNumber($categoryData['code']),
            ]);

            foreach ($categoryData['subcategories'] as $index => $subcategoryData) {
                Category::create([
                    'name'            => $subcategoryData['name'],
                    'code'            => $subcategoryData['code'],
                    'slug'            => Str::slug($subcategoryData['name']),
                    'type'            => 'sub',
                    'parent_id'       => $category->id,
                    'ordering_number' => $index + 1,
                    'status'          => 'active',
                ]);
            }
        }
    }

    protected function getOrderingNumber(string $code): int
    {
        return [
            'H' => 1,
            'B' => 2,
            'F' => 3,
            'S' => 4,
            'W' => 5,
            'P' => 6,
            'E' => 7,
            'G' => 8,
        ][$code] ?? 999;
    }
}