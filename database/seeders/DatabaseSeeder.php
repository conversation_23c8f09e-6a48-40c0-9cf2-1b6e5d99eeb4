<?php

namespace Database\Seeders;

use App\Models\Herb;
use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call(RolePermissionSeeder::class);
        $this->call( UserSeeder::class,);
        $this->call( CategorySeeder::class,);
        $this->call( AuthClient::class,);
        $this->call( CategorySeeder::class,);
        $this->call( ProductClassSeeder::class,);
        $this->call(AttributeSeeder::class,);
        $this->call(DropdownSeeder::class,);
        $this->call(BrandSeeder::class,);
        $this->call(VendorSeeder::class,);
        $this->call(ProductSeeder::class,);
    }
}
