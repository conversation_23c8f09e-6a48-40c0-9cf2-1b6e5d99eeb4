<?php

namespace App\Services;

use App\Models\SupportTopic;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportTopicService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportTopic::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportTopicData($request);

        return SupportTopic::create($data);
    }

    private function prepareSupportTopicData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new SupportTopic())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'supportTopic')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'supportTopic');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'supportTopic');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): SupportTopic
    {
        return SupportTopic::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $supportTopic = SupportTopic::findOrFail($id);
        $updateData = $this->prepareSupportTopicData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportTopic->update($updateData);

        return $supportTopic;
    }

    public function destroy(int $id): bool
    {
        $supportTopic = SupportTopic::findOrFail($id);
        return $supportTopic->delete();
    }
}
