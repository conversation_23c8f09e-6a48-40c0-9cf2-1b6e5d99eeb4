<?php

namespace App\Services;

use App\Models\ProductClass;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class ProductClassService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = ProductClass::query();
        $query->with(['category:id,name,code','subCategory:id,name,code','parent:id,name,code']);

        // Select specific columns
        $query->select(['*']);

        // Apply filters
        $this->applyProductClassFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareProductClassData($request);

        return ProductClass::create($data);
    }

    private function prepareProductClassData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new ProductClass())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'productClass');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'productClass');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ProductClass
    {
        return ProductClass::with(['category:id,name,code','subCategory:id,name,code'])
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {
        $productClass = ProductClass::findOrFail($id);
        $updateData = $this->prepareProductClassData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $productClass->update($updateData);

        return $productClass;
    }

    public function destroy(int $id): bool
    {
        $productClass = ProductClass::findOrFail($id);
        return $productClass->delete();
    }

    public function getClassByCategory(int $categoryId): Collection
    {
        return ProductClass::where('category_id', $categoryId)
            ->where('status', 'active')
            ->whereNull('parent_id') // Only get top-level classes
            ->select(['id', 'name', 'status','code', 'parent_id','is_popular'])
            ->get();
    }

    public function getSubClasses(int $classId): Collection
    {
        return ProductClass::where('parent_id', $classId)
            ->where('status', 'active')
            ->select(['id', 'name', 'status','code', 'parent_id','is_popular'])
            ->get();
    }

    public function classListByActive($request): Collection
    {
        $query = ProductClass::where('status', 'active')
            ->whereNull('parent_id')
            ->select(['id', 'name', 'status','code', 'parent_id','is_popular']);

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('sub_category_id')) {
            $query->where('sub_category_id', $request->sub_category_id);
        }

        return $query->get();
    }

    /**
     * Apply product class specific filters
     */
    private function applyProductClassFilters($query, Request $request): void
    {
        // Filter by parent classes (is_parent = true means classes that have children)
        if ($request->has('is_parent')) {
            $isParent = filter_var($request->input('is_parent'), FILTER_VALIDATE_BOOLEAN);
            if ($isParent) {
                // Classes that have children
                $query->whereHas('subClasses');
            } else {
                // Classes that don't have children (leaf classes)
                $query->whereDoesntHave('subClasses');
            }
        }

        // Filter by parent class ID
        if ($request->filled('parent_id')) {
            if ($request->input('parent_id') === 'null' || $request->input('parent_id') === null) {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->input('parent_id'));
            }
        }

        // Filter by category ID
        if ($request->filled('category_id')) {
            $query->where('category_id', $request->input('category_id'));
        }

        // Filter by sub category ID
        if ($request->filled('sub_category_id')) {
            $query->where('sub_category_id', $request->input('sub_category_id'));
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        // Filter by is_popular
        if ($request->has('is_popular')) {
            $isPopular = filter_var($request->input('is_popular'), FILTER_VALIDATE_BOOLEAN);
            $query->where('is_popular', $isPopular);
        }
    }
};
