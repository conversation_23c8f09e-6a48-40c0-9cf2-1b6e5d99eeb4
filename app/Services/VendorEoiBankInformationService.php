<?php

namespace App\Services;

use App\Models\VendorEoiBankInformation;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorEoiBankInformationService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorEoiBankInformation::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorEoiBankInformationData($request);

        return VendorEoiBankInformation::create($data);
    }

    private function prepareVendorEoiBankInformationData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorEoiBankInformation())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorEoiBankInformation')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorEoiBankInformation');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorEoiBankInformation');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorEoiBankInformation
    {
        return VendorEoiBankInformation::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorEoiBankInformation = VendorEoiBankInformation::findOrFail($id);
        $updateData = $this->prepareVendorEoiBankInformationData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorEoiBankInformation->update($updateData);

        return $vendorEoiBankInformation;
    }

    public function getEoiBankList($request, $vendor_eoi_id){
        return VendorEoiBankInformation::where('vendor_eoi_id', $vendor_eoi_id)->get();
    }

    public function destroy(int $id): bool
    {
        $vendorEoiBankInformation = VendorEoiBankInformation::findOrFail($id);
        return $vendorEoiBankInformation->delete();
    }
}
