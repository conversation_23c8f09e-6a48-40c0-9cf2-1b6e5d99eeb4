<?php

namespace App\Services;

use App\Models\VendorEoiLocation;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorEoiLocationService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorEoiLocation::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorEoiLocationData($request);

        return VendorEoiLocation::create($data);
    }

    private function prepareVendorEoiLocationData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorEoiLocation())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorEoiLocation')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorEoiLocation');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorEoiLocation');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorEoiLocation
    {
        return VendorEoiLocation::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorEoiLocation = VendorEoiLocation::findOrFail($id);
        $updateData = $this->prepareVendorEoiLocationData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorEoiLocation->update($updateData);

        return $vendorEoiLocation;
    }

    public function locationList($request, $vendor_eoi_id){
        return VendorEoiLocation::where('vendor_eoi_id', $vendor_eoi_id)->get();
    }

    public function destroy(int $id): bool
    {
        $vendorEoiLocation = VendorEoiLocation::findOrFail($id);
        return $vendorEoiLocation->delete();
    }
}
