<?php

namespace App\Services;

use App\Models\SupportReason;
use App\Models\SupportTicket;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportTicketService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportTicket::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportTicketData($request);

        $ticket = SupportTicket::create($data);

        return $ticket->load(['reason', 'category', 'topic']);
    }

    private function prepareSupportTicketData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new SupportTicket())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['user_id'] = auth()->id();
            $data['created_at'] = now();

            // Generate code
            $reason = SupportReason::find($data['reason_id'] ?? null);
            $prefix = $reason?->code_prefix ?? 'CA';
            $date = now()->format('ymd');
            $sequence = str_pad((\App\Models\SupportTicket::withTrashed()->max('id') + 1), 6, '0', STR_PAD_LEFT);
            $data['code'] = $prefix . $date . $sequence;

            // Auto-assign resolver
            switch ($reason?->route_to) {
                case 'vendor':
                    $data['assigned_to'] = $data['vendor_id'] ?? null;
                    break;
                case 'tpl':
                    $data['assigned_to'] = $data['tpl_id'] ?? null; // requires tpl_id to be part of ticket
                    break;
                case 'admin':
                default:
                    $data['assigned_to'] = $this->getAdminUserId(); // implement this method as needed
                    break;
            }

            // Optional: Admin CC logic could go here (notifications etc.)
            if (in_array($reason?->route_to, ['vendor', 'tpl'])) {
               // $this->notifyAdminCC($data);
            }
        }

        return $data;
    }

    private function getAdminUserId(): ?int
    {
        return \App\Models\User::role('admin')->first()?->id;
    }

    /*private function notifyAdminCC(array $ticketData): void
    {
        $adminEmails = \App\Models\User::role('admin')->pluck('email')->toArray();
        \Illuminate\Support\Facades\Notification::route('mail', $adminEmails)
            ->notify(new \App\Notifications\SupportTicketCcNotification($ticketData));
    }*/

    public function show(int $id): SupportTicket
    {
        return SupportTicket::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $supportTicket = SupportTicket::findOrFail($id);
        $updateData = $this->prepareSupportTicketData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportTicket->update($updateData);

        return $supportTicket;
    }

    public function destroy(int $id): bool
    {
        $supportTicket = SupportTicket::findOrFail($id);
        return $supportTicket->delete();
    }
}
