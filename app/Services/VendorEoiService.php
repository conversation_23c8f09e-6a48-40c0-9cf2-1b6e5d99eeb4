<?php

namespace App\Services;

use App\Models\VendorEoi;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Mail\VendorEOISubmission;
use Illuminate\Support\Facades\Mail;

class VendorEoiService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorEoi::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorEoiData($request);

        $vendor = VendorEoi::create($data);
        // Send email notification

        Mail::to($vendor->spoc_email)->queue(new VendorEOISubmission($vendor));

        return $vendor;
    }

    private function prepareVendorEoiData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new VendorEoi())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        $data['veoi_id'] = $this->generateVeoiId($data['name_tl_en']);

        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorEoi')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorEoi');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorEoi');

        // $data['director_passport_copy'] = $this->s3FileUpload($request, 'director_passport_copy', 'vendor_eoi')['path'] ?? null;
        // $data['director_emirates_id_copy'] = $this->s3FileUpload($request, 'director_emirates_id_copy', 'vendor_eoi')['path'] ?? null;
        // $data['spoc_passport_copy'] = $this->s3FileUpload($request, 'spoc_passport_copy', 'vendor_eoi')['path'] ?? null;
        // $data['spoc_emirates_id_copy'] = $this->s3FileUpload($request, 'spoc_emirates_id_copy', 'vendor_eoi')['path'] ?? null;
        // $data['spoc_loa_copy'] = $this->s3FileUpload($request, 'spoc_loa_copy', 'vendor_eoi')['path'] ?? null;
        
        // Add created_by and created_at fields for new records
        if ($isNew) {
            //$data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorEoi
    {
        return VendorEoi::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorEoi = VendorEoi::findOrFail($id);
        $updateData = $this->prepareVendorEoiData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorEoi->update($updateData);

        return $vendorEoi;
    }

    public function destroy(int $id): bool
    {
        $vendorEoi = VendorEoi::findOrFail($id);
        return $vendorEoi->delete();
    }

    public function approveVendorEoi(Request $request)
    {
        return $vendorEoi = VendorEoi::findOrFail($request->input('id'))->update(['approval_status' => 'Approved', 'approved_by' => auth()->user()->id]);
    }

    private function generateVeoiId(string $name): string
    {
        $date = Carbon::now()->format('Ymd'); 
        $shortName = strtoupper(Str::slug(Str::limit($name, 6, ''), ''));
        $prefix = "VEOI#{$date}{$shortName}";

        $existingCount = VendorEoi::where('veoi_id', 'like', "{$prefix}%")->count() + 1;
        $serial = str_pad($existingCount, 3, '0', STR_PAD_LEFT);

        $veoiId = $prefix . $serial;
        return str_pad($veoiId, 20, 'X');
    }
}
