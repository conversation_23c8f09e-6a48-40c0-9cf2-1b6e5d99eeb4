<?php

namespace App\Services;

use App\Models\VendorEoi;
use App\Models\Category;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Mail\VendorEOISubmission;
use Illuminate\Support\Facades\Mail;

class VendorEoiService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorEoi::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_tl_en', 'name_tl_ar', 'spoc_email', 'veoi_id']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Filtering by approval status
        if ($request->filled('approval_status')) {
            $query->where('approval_status', $request->approval_status);
        }

        // Pagination
        $result = $this->paginateOrGet($query, $request);

        // Format response if it's a collection
        if ($result instanceof Collection) {
            return $result->map(function ($vendorEoi) {
                return $this->formatVendorEoiResponse($vendorEoi);
            })->toArray();
        }

        // Format paginated results
        if (isset($result->items)) {
            $result->getCollection()->transform(function ($vendorEoi) {
                return $this->formatVendorEoiResponse($vendorEoi);
            });
        }

        return $result;
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorEoiData($request);

        $vendor = VendorEoi::create($data);
        // Send email notification

        Mail::to($vendor->spoc_email)->queue(new VendorEOISubmission($vendor));

        return $this->formatVendorEoiResponse($vendor);
    }

    private function prepareVendorEoiData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new VendorEoi())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Generate VEOI ID for new records
        if ($isNew) {
            $data['veoi_id'] = $this->generateVeoiId($data['name_tl_en']);
            $data['created_at'] = now();
        }

        // Handle categories_to_sell conversion from array to comma-separated string
        if (isset($data['categories_to_sell']) && is_array($data['categories_to_sell'])) {
            // Convert array of category IDs to comma-separated string
            $data['categories_to_sell'] = implode(',', $data['categories_to_sell']);
        }

        // Ensure JSON fields are properly handled
        if (isset($data['business_data']) && is_array($data['business_data'])) {
            $data['business_data'] = $data['business_data'];
        }

        if (isset($data['order_collection_location']) && is_array($data['order_collection_location'])) {
            $data['order_collection_location'] = $data['order_collection_location'];
        }

        return $data;
    }

    public function show(int $id): array
    {
        $vendorEoi = VendorEoi::with(['approver:id,name'])->findOrFail($id);
        return $this->formatVendorEoiResponse($vendorEoi);
    }

    public function update( $request, int $id)
    {
        $vendorEoi = VendorEoi::findOrFail($id);
        $updateData = $this->prepareVendorEoiData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorEoi->update($updateData);
        $vendorEoi->refresh();

        return $this->formatVendorEoiResponse($vendorEoi);
    }

    public function destroy(int $id): bool
    {
        $vendorEoi = VendorEoi::findOrFail($id);
        return $vendorEoi->delete();
    }

    public function approveVendorEoi(Request $request)
    {
        return $vendorEoi = VendorEoi::findOrFail($request->input('id'))->update(['approval_status' => 'Approved', 'approved_by' => auth()->user()->id]);
    }

    private function generateVeoiId(string $name): string
    {
        $date = Carbon::now()->format('Ymd'); 
        $shortName = strtoupper(Str::slug(Str::limit($name, 6, ''), ''));
        $prefix = "VEOI#{$date}{$shortName}";

        $existingCount = VendorEoi::where('veoi_id', 'like', "{$prefix}%")->count() + 1;
        $serial = str_pad($existingCount, 3, '0', STR_PAD_LEFT);

        $veoiId = $prefix . $serial;
        return str_pad($veoiId, 20, 'X');
    }

    /**
     * Format vendor EOI response with category data
     */
    private function formatVendorEoiResponse($vendorEoi): array
    {
        $data = $vendorEoi->toArray();

        // Convert categories_to_sell from comma-separated string to array of objects
        if (!empty($data['categories_to_sell'])) {
            $categoryIds = explode(',', $data['categories_to_sell']);
            // Filter out empty values and convert to integers
            $categoryIds = array_filter(array_map('intval', $categoryIds));

            if (!empty($categoryIds)) {
                $categories = Category::whereIn('id', $categoryIds)
                    ->select('id', 'name', 'slug')
                    ->get()
                    ->map(function ($category) {
                        return [
                            'id' => $category->id,
                            'name' => $category->name,
                            'slug' => $category->slug
                        ];
                    });
                $data['categories_to_sell'] = $categories->toArray();
            } else {
                $data['categories_to_sell'] = [];
            }
        } else {
            $data['categories_to_sell'] = [];
        }

        return $data;
    }
}
