<?php

namespace App\Services;

use App\Models\SupportTicketMessage;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportTicketMessageService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportTicketMessage::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportTicketMessageData($request);

        $message = SupportTicketMessage::create($data);

        if ($request->hasFile('attachments')) {
            foreach ($request->file('attachments') as $file) {
                $upload = $this->s3FileUpload($request, 'attachments', 'supportTicketMessage', $file);

                $message->attachments()->create([
                    'file_path' => $upload['path'] ?? null,
                    'file_name' => $file->getClientOriginalName(),
                    'mime_type' => $file->getClientMimeType(),
                    'file_size' => $file->getSize(),
                ]);
            }
        }

        return $message->load('attachments');
    }

    private function prepareSupportTicketMessageData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $fillable = (new SupportTicketMessage())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));

        if ($isNew) {
            $data['sender_id'] = auth()->id();
            $data['sender_type'] = get_class(auth()->user());
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): SupportTicketMessage
    {
        return SupportTicketMessage::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $supportTicketMessage = SupportTicketMessage::findOrFail($id);
        $updateData = $this->prepareSupportTicketMessageData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportTicketMessage->update($updateData);

        if ($request->hasFile('attachments')) {
            // Delete old attachments
            foreach ($supportTicketMessage->attachments as $attachment) {
                Storage::disk('public')->delete($attachment->file_path);
                $attachment->delete();
            }

            // Upload new attachments
            foreach ($request->file('attachments') as $file) {
                $upload = $this->s3FileUpload($request, 'attachments', 'supportTicketMessage', $file);

                $supportTicketMessage->attachments()->create([
                    'file_path' => $upload['path'] ?? null,
                    'file_name' => $file->getClientOriginalName(),
                    'mime_type' => $file->getClientMimeType(),
                    'file_size' => $file->getSize(),
                ]);
            }
        }

        return $supportTicketMessage->load('attachments');
    }

    public function destroy(int $id): bool
    {
        $supportTicketMessage = SupportTicketMessage::findOrFail($id);
        return $supportTicketMessage->delete();
    }
}
