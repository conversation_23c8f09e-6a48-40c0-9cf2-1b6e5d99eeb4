<?php

namespace App\Services;

use App\Models\Permission;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class PermissionService
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = Permission::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->preparePermissionData($request);

        return Permission::create($data);
    }

    private function preparePermissionData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new Permission())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'permission');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'permission');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            // $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Permission
    {
        return Permission::findOrFail($id);
    }

    public function update(Request $request, int $id)
    {
        $permission = Permission::findOrFail($id);
        $updateData = $this->preparePermissionData($request, false);
        $permission->update($updateData);

        return $permission;
    }

    public function destroy(int $id): bool
    {
        $permission = Permission::findOrFail($id);
        return $permission->delete();
       
    }

    public function permissionList(Request $request)
    {
        $permissions = Permission::get();
        return $permissions->groupBy('group_name');
    }
}
