<?php

namespace App\Services;

use App\Models\Page;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class PageService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Page::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['title_en','title_ar']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->preparePageData($request);

        return Page::create($data);
    }

    private function preparePageData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Page())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'page');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'page');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Page
    {
        return Page::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $page = Page::findOrFail($id);
        $updateData = $this->preparePageData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $page->update($updateData);

        return $page;
    }

    public function destroy(int $id): bool
    {
        $page = Page::findOrFail($id);
        return $page->delete();
    }
}
