<?php

namespace App\Services;

use App\Models\Category;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class CategoryService
{
    use HelperTrait;

    public function index(Request $request): Collection|LengthAwarePaginator|array
    {
        $query = Category::query();

        // Select specific columns
        $query->select(['*']);

        // Apply filters
        $this->applyCategoryFilters($query, $request);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareCategoryData($request);

        return Category::create($data);
    }

    private function prepareCategoryData(Request $request, bool $isNew = true): array
    {
        // Get the fillable fields from the model
        $fillable = (new Category())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = $request->only($fillable);

        // Handle file uploads
        // $data['icon'] = $this->s3FileUpload($request, 'icon', 'category')['path'] ?? null;
        // $data['cover_image'] = $this->s3FileUpload($request, 'cover_image', 'category')['path'] ?? null;
        // $data['banner'] = $this->s3FileUpload($request, 'banner', 'category')['path'] ?? null;
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'category');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Category
    {
        return Category::findOrFail($id);
    }

    public function update(Request $request, int $id)
    {
        $category = Category::findOrFail($id);
        $updateData = $this->prepareCategoryData($request, false);
        $category->update($updateData);

        return $category;
    }

    public function destroy(int $id): bool
    {
        $category = Category::findOrFail($id);
        return $category->delete();
       
    }

    public function categoryListByActive($request)
    {
        $query = Category::where('status', 'active')
            ->where('parent_id', null)
            ->select(['id', 'name', 'slug', 'icon','type','parent_id','code'])
            ->get();
        return $query;
    }

    public function getSubCategories(int $categoryId)
    {
        $query = Category::where('parent_id', $categoryId)
            ->where('status', 'active')
            ->select(['id', 'name', 'slug', 'icon','type','parent_id','code'])
            ->get();

        return $query;
    }

    /**
     * Apply category-specific filters
     */
    private function applyCategoryFilters($query, Request $request): void
    {
        // Filter by parent categories (is_parent = true means parent_id is null)
        if ($request->has('is_parent')) {
            $isParent = filter_var($request->input('is_parent'), FILTER_VALIDATE_BOOLEAN);
            if ($isParent) {
                // Parent categories (parent_id is null)
                $query->whereNull('parent_id');
            } else {
                // Child categories (parent_id is not null)
                $query->whereNotNull('parent_id');
            }
        }

        // Filter by parent category ID
        if ($request->filled('parent_category')) {
            $query->where('parent_id', $request->input('parent_category'));
        }

        // Filter by category type
        if ($request->filled('type')) {
            $query->where('type', $request->input('type'));
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }
    }
}
