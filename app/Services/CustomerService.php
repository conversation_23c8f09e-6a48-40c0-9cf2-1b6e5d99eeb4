<?php

namespace App\Services;

use App\Models\Customer;
use App\Models\User;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CustomerService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Customer::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store($request)
    {
        DB::beginTransaction();
        try {
            $data = $this->prepareCustomerData($request);
            $userData = new User();
            $userData->fill($data);
            $userData->save();
            $userData->assignRole('customer'); // Assign a default role to the user

            $data['user_id'] = $userData->id; // Link the customer to the user
            $customer = Customer::create($data);
            DB::commit();
            return $customer->load('user'); // Load the user relationship;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareCustomerData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        $data = $validated;

        //password hash
        if (isset($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        }

        $data['avatar'] = $this->s3FileUpload($request, 'avatar', 'customer')['path'] ?? null;

        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Customer
    {
        return Customer::with('user')->findOrFail($id);
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();

        try {
            $customer = Customer::with('user')->findOrFail($id);
            $updateData = $this->prepareCustomerData($request, false);

            $customerUpdateData = array_filter($updateData, function ($value) {
                return !is_null($value);
            });

            $customer->update($customerUpdateData);
            $userUpdateData = array_intersect_key($updateData, array_flip([
                'name',
                'email',
                'phone',
                'password',
                'avatar',
                'status',
                'is_active',
            ]));

            $userUpdateData = array_filter($userUpdateData, function ($value) {
                return !is_null($value);
            });

            if (!empty($userUpdateData)) {
                $customer->user->update($userUpdateData);
            }

            DB::commit();

            return $customer->refresh();
        } catch (\Throwable $th) {
            DB::rollBack();

            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $customer = Customer::findOrFail($id);
        return $customer->delete();
    }
}
