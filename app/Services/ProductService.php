<?php

namespace App\Services;

use App\Models\Product;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ProductService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Product::query();
        $query->with('category:id,name,code', 'subCategory:id,name,code', 'productClass:id,name,code', 'subClass:id,name,code');

        // Select specific columns
        $query->select(['id', 'title_en', 'title_en', 'is_variant', 'regular_price', 'offer_price', 'category_id', 'sub_category_id', 'class_id', 'sub_class_id', 'created_at', 'updated_at']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareProductData($request);

        return Product::create($data);
    }

    private function prepareProductData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Product())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'product')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'product');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'product');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Product
    {
        return Product::with(['productMedia:id,product_id,product_variant_id,type,path,title,alt_text,lang_code,position,is_primary', 'productSeo', 'productFaqs', 'fulfillment', 'productVariants', 'productVariants.productVariantAttribute:id,product_variant_id,product_attribute_id,product_attribute_value_id', 'productVariants.productVariantAttribute.attribute:id,name,name_ar', 'productVariants.productVariantAttribute.attributeValue:id,product_attribute_id,value,value_ar'])->findOrFail($id);
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();
        try {
            $product = Product::findOrFail($id);
            $updateData = $this->prepareProductData($request, false);

            $updateData = array_filter($updateData, function ($value) {
                return !is_null($value);
            });

            $product->update($updateData);
            if ($request->has('product_seo')) {
                $product->productSeo()->updateOrCreate(
                    ['product_id' => $id],
                    $request->input('product_seo')
                );
            }
            if ($request->has('product_faqs')) {
                $product->productFaqs()->delete();
                $faqDataList = [];
                foreach ($request->input('product_faqs') as $faq) {
                    $faqDataList[] = array_merge($faq, [
                        'product_id' => $id,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
                $product->productFaqs()->insert($faqDataList);
            }

            //product fulfillments
            if ($request->has('product_fulfillment')) {
                $product->fulfillment()->updateOrCreate(
                    ['product_id' => $id],
                    $request->input('product_fulfillment')
                );
            }

            // Sync product variants and their attributes
            if ($request->has('product_variants') && $request->input('is_variant') == true) {
                $this->syncProductVariants($product, $request->input('product_variants'));
            }
            DB::commit();

            return $product->load('productSeo', 'productFaqs', 'fulfillment', 'productVariants.productVariantAttribute',);
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id): bool
    {
        $product = Product::findOrFail($id);
        return $product->delete();
    }

    protected function syncProductVariants(Product $product, array $variants): void
    {
        // Step 1: Delete old data
        $variantIds = $product->productVariants()->pluck('id');

        if ($variantIds->isNotEmpty()) {
            DB::table('product_variant_attributes')->whereIn('product_variant_id', $variantIds)->delete();
            $product->productVariants()->whereIn('id', $variantIds)->delete();
        }

        // Step 2: Prepare bulk insert data
        $variantInsertData = [];
        $attributeInsertData = [];
        $now = now();

        foreach ($variants as $variantData) {
            $attributeId = $variantData['attribute_id'] ?? null;
            $attributeValueId = $variantData['attribute_value_id'] ?? null;

            $baseVariant = Arr::except($variantData, ['attribute_id', 'attribute_value_id']);
            $baseVariant['product_id'] = $product->id;
            $baseVariant['created_at'] = $now;
            $baseVariant['updated_at'] = $now;

            $variantInsertData[] = [
                'data' => $baseVariant,
                'attribute_id' => $attributeId,
                'attribute_value_id' => $attributeValueId,
            ];
        }

        // Step 3: Insert variants and get IDs
        foreach ($variantInsertData as $entry) {
            $variant = $product->productVariants()->create($entry['data']);

            if ($entry['attribute_id'] && $entry['attribute_value_id']) {
                $attributeInsertData[] = [
                    'product_variant_id' => $variant->id,
                    'product_attribute_id' => $entry['attribute_id'],
                    'product_attribute_value_id' => $entry['attribute_value_id'],
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }

            if (isset($entry['data']['path'])) {
                $variant->media()->create([
                    'product_id' => $product->id,
                    'product_variant_id' => $variant->id,
                    'type' => 'image',
                    'path' => $entry['data']['path'],
                ]);
            }
        }

        // Step 4: Bulk insert attribute mappings
        if (!empty($attributeInsertData)) {
            DB::table('product_variant_attributes')->insert($attributeInsertData);
        }
    }
}
