<?php

namespace App\Services;

use App\Models\SupportCategory;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class SupportCategoryService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = SupportCategory::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareSupportCategoryData($request);

        return SupportCategory::create($data);
    }

    private function prepareSupportCategoryData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new SupportCategory())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'supportCategory')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'supportCategory');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'supportCategory');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): SupportCategory
    {
        return SupportCategory::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $supportCategory = SupportCategory::findOrFail($id);
        $updateData = $this->prepareSupportCategoryData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $supportCategory->update($updateData);

        return $supportCategory;
    }

    public function destroy(int $id): bool
    {
        $supportCategory = SupportCategory::findOrFail($id);
        return $supportCategory->delete();
    }
}
