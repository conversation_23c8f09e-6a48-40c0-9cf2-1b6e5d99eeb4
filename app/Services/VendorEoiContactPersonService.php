<?php

namespace App\Services;

use App\Models\VendorEoiContactPerson;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class VendorEoiContactPersonService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = VendorEoiContactPerson::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareVendorEoiContactPersonData($request);

        return VendorEoiContactPerson::create($data);
    }

    private function prepareVendorEoiContactPersonData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new VendorEoiContactPerson())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'vendorEoiContactPerson')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'vendorEoiContactPerson');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'vendorEoiContactPerson');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            //$data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): VendorEoiContactPerson
    {
        return VendorEoiContactPerson::findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $vendorEoiContactPerson = VendorEoiContactPerson::findOrFail($id);
        $updateData = $this->prepareVendorEoiContactPersonData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $vendorEoiContactPerson->update($updateData);

        return $vendorEoiContactPerson;
    }

    public function contactPersonList($request, $vendor_eoi_id){
        return VendorEoiContactPerson::where('vendor_eoi_id', $vendor_eoi_id)->get();
    }

    public function destroy(int $id): bool
    {
        $vendorEoiContactPerson = VendorEoiContactPerson::findOrFail($id);
        return $vendorEoiContactPerson->delete();
    }
}
