<?php

namespace App\Services;

use App\Models\Banner;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BannerService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Banner::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareBannerData($request);

        return Banner::create($data);
    }

    private function prepareBannerData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Banner())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));


        // Handle file uploads
        //$data['featured_image'] = $this->s3FileUpload($request, 'featured_image', 'banner')['path'] ?? null;
        //$data['thumbnail'] = $this->ftpFileUpload($request, 'thumbnail', 'banner');
        //$data['cover_picture'] = $this->ftpFileUpload($request, 'cover_picture', 'banner');

        // Add created_by and created_at fields for new records
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Banner
    {
        return Banner::with('items')->findOrFail($id);
    }

    public function update( $request, int $id)
    {
        $banner = Banner::findOrFail($id);
        $updateData = $this->prepareBannerData($request, false);

         $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $banner->update($updateData);

        return $banner;
    }

    public function destroy(int $id): bool
    {
        $banner = Banner::findOrFail($id);
        return $banner->delete();
    }
}
