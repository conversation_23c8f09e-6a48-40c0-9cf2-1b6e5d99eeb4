<?php

namespace App\Services;

use App\Models\Brand;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

class BrandService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Brand::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en', 'name_ar', 'slug', 'country_of_origin']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store(Request $request)
    {
        $data = $this->prepareBrandData($request);

        return Brand::create($data);
    }

    /**
     * @throws \Exception
     */
    private function prepareBrandData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new Brand())->getFillable();

        // Extract relevant fields from the request dynamically
        $data = array_intersect_key($validated, array_flip($fillable));

        // Handle file uploads for supporting documents
        if ($request->hasFile('logo')) {
            $data['logo'] = $this->s3FileUpload($request, 'logo', 'brand/logos')['path'] ?? null;
        }

        if ($request->hasFile('trademark_document')) {
            $data['trademark_document'] = $this->s3FileUpload($request, 'trademark_document', 'brand/documents')['path'] ?? null;
        }

        if ($request->hasFile('relationship_proof')) {
            $data['relationship_proof'] = $this->s3FileUpload($request, 'relationship_proof', 'brand/documents')['path'] ?? null;
        }

        if ($request->hasFile('purchase_proof')) {
            $data['purchase_proof'] = $this->s3FileUpload($request, 'purchase_proof', 'brand/documents')['path'] ?? null;
        }

        if ($request->hasFile('self_declaration')) {
            $data['self_declaration'] = $this->s3FileUpload($request, 'self_declaration', 'brand/documents')['path'] ?? null;
        }

        // Handle multiple product pictures upload
        if ($request->hasFile('product_pictures')) {
            $productPictures = [];
            foreach ($request->file('product_pictures') as $file) {
                $uploadResult = $this->s3FileUpload(['product_pictures' => $file], 'product_pictures', 'brand/products');
                if (isset($uploadResult['path'])) {
                    $productPictures[] = $uploadResult['path'];
                }
            }
            $data['product_pictures'] = $productPictures;
        }

        // Handle JSON fields properly
        if (isset($data['top_products']) && is_array($data['top_products'])) {
            $data['top_products'] = json_encode($data['top_products']);
        }

        // Add created_by and created_at fields for new records
        if ($isNew) {
            if (auth()->check()) {
                $data['user_id'] = auth()->user()->id;
            }
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): Brand
    {
        return Brand::findOrFail($id);
    }

    public function update($request, int $id)
    {
        $brand = Brand::findOrFail($id);
        $updateData = $this->prepareBrandData($request, false);

        $updateData = array_filter($updateData, function ($value) {
            return !is_null($value);
        });

        $brand->update($updateData);

        return $brand;
    }

    public function destroy(int $id): bool
    {
        $brand = Brand::findOrFail($id);
        return $brand->delete();
    }

    public function brandListByActive($request)
    {
        $brandQuery = Brand::where('status', 'approved')
            ->where('is_active', true)
            ->select(['id', 'name_en', 'name_ar', 'slug', 'logo', 'country_of_origin'])
            ->get();
        return $brandQuery;
    }
}
