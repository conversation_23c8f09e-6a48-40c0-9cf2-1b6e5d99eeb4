<?php

namespace App\Services;

use App\Models\ProductAttribute;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProductAttributeService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = ProductAttribute::query();

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store($request)
    {
        DB::beginTransaction();
        try {
            $productAttribute = ProductAttribute::create(
                $this->prepareProductAttributeData($request)
            );

            $productAttribute->values()->createMany(
                collect($request->input('values'))->map(fn($item) => [
                    'value' => $item['value'],
                    'value_ar' => $item['value_ar'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ])->toArray()
            );

            DB::commit();
            return $productAttribute->load('values');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    private function prepareProductAttributeData($request, bool $isNew = true): array
    {
        $validated = $request->validated();
        // Get the fillable fields from the model
        $fillable = (new ProductAttribute())->getFillable();
        $data = array_intersect_key($validated, array_flip($fillable));
        if ($isNew) {
            $data['user_id'] = auth()->user()->id;
            $data['created_at'] = now();
        }

        return $data;
    }

    public function show(int $id): ProductAttribute
    {
        return ProductAttribute::with('values')
            ->findOrFail($id);
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();
        try {
            $productAttribute = ProductAttribute::findOrFail($id);
            $updateData = array_filter(
                $this->prepareProductAttributeData($request, false),
                fn($value) => !is_null($value)
            );
            $productAttribute->update($updateData);

            if ($request->has('values')) {
                $valuesData = collect($request->input('values'))->map(function ($item) {
                    return [
                        'value' => $item['value'],
                        'value_ar' => $item['value_ar'] ?? null,
                    ];
                });
                $productAttribute->values()->delete();
                $productAttribute->values()->createMany($valuesData->toArray());
            }

            DB::commit();
            return $productAttribute->load('values');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }



    public function destroy(int $id): bool
    {
        DB::beginTransaction();
        try {
            $productAttribute = ProductAttribute::findOrFail($id);
            $productAttribute->values()->delete();
            $result = $productAttribute->delete();
            DB::commit();
            return $result;
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }
}
