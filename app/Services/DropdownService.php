<?php

namespace App\Services;

use App\Models\Dropdown;
use App\Traits\HelperTrait;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class DropdownService
{
    use HelperTrait;

    public function index($request): Collection|LengthAwarePaginator|array
    {
        $query = Dropdown::query();

        // Load relationships
        $query->with('options');

        // Select specific columns
        $query->select(['*']);

        // Sorting
        $this->applySorting($query, $request);

        // Searching
        $searchKeys = ['name_en', 'name_ar', 'slug']; // Define the fields you want to search by
        $this->applySearch($query, $request->input('search'), $searchKeys);

        // Pagination
        return $this->paginateOrGet($query, $request);
    }

    public function store($request)
    {
        DB::beginTransaction();
        try {
            $dropdown = Dropdown::create(
                $this->prepareDropdownData($request)
            );

            $dropdown->options()->createMany(
                collect($request->input('options'))->map(fn($item) => [
                    'value_en' => $item['value_en'],
                    'value_ar' => $item['value_ar'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ])->toArray()
            );

            DB::commit();
            return $dropdown->load('options');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function show(int $id)
    {
        return Dropdown::with('options')->findOrFail($id);
    }

    public function update($request, int $id)
    {
        DB::beginTransaction();
        try {
            $dropdown = Dropdown::findOrFail($id);
            $dropdown->update($this->prepareDropdownData($request));

            // Delete existing options and create new ones
            $dropdown->options()->delete();
            $dropdown->options()->createMany(
                collect($request->input('options'))->map(fn($item) => [
                    'value_en' => $item['value_en'],
                    'value_ar' => $item['value_ar'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ])->toArray()
            );

            DB::commit();
            return $dropdown->load('options');
        } catch (\Throwable $th) {
            DB::rollBack();
            throw $th;
        }
    }

    public function destroy(int $id)
    {
        $dropdown = Dropdown::findOrFail($id);
        $dropdown->delete();
    }

    private function prepareDropdownData($request): array
    {
        return [
            'name_en' => $request->input('name_en'),
            'name_ar' => $request->input('name_ar'),
            'slug' => $request->input('slug') ?: Str::slug($request->input('name_en')),
        ];
    }
}
