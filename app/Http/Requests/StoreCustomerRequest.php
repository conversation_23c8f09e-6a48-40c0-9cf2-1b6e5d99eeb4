<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email',
            'password' => 'required|string|min:8|confirmed',
            'phone' => 'nullable|string|max:15',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'status' => 'required|in:pending,active,inactive,banned',
            'is_active' => 'nullable|in:0,1',
            'gender' => 'nullable|in:male,female,other',
            'date_of_birth' => 'nullable|date',
            'loyalty_points' => 'nullable|integer|min:0',
            'customer_type' => 'nullable|in:retail,wholesale',
            'preferred_language' => 'nullable|in:en,ar,fr,de,es', // Add more languages as needed
            'preferred_currency' => 'nullable|in:AED,USD,EUR,GBP', // Add more currencies as needed

        ];
    }
}
