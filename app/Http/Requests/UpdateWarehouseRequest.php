<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWarehouseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vendor_id' => 'nullable|exists:vendors,id',
            'name' => 'required|string|max:255',
            'code' => 'nullable|string|max:50|unique:warehouses,code,' . $this->route('warehouse'),
            'address' => 'required|string|max:1000',
            'location' => 'nullable|string|max:1000',
            'contact_person' => 'nullable|string|max:100',
            'contact_number' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'status' => 'required|in:active,inactive',
        ];
    }
}
