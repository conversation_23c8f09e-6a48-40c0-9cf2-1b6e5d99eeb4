<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateSupportTopicRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'category_id' => 'required|exists:support_categories,id',
            'name' => 'required|string|max:255|unique:support_topics,name,' . $this->route('support_topic'),
            'status' => 'required|in:active,inactive',
        ];
    }
}
