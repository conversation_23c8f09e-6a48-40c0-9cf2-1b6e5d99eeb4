<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VerifyOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'email' => 'required_without:phone|nullable|email|exists:users,email',
            'phone' => 'required_without:email|nullable|exists:users,phone',
            'otp' => 'required|string|size:6',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'email.required_without' => 'Either email or phone is required.',
            'email.email' => 'Please provide a valid email address.',
            'email.exists' => 'This email is not registered in our system.',
            'phone.required_without' => 'Either email or phone is required.',
            'phone.exists' => 'This phone number is not registered in our system.',
            'otp.required' => 'OTP is required.',
            'otp.size' => 'OTP must be 6 digits.',
        ];
    }
}
