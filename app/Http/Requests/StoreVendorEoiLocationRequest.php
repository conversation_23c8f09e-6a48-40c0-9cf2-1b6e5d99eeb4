<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVendorEoiLocationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vendor_eoi_id' => 'required|exists:vendor_eoi,id',
            'type' => 'required|in:office,retail_outlet,warehouse',
            'address' => 'nullable|string',
            'map_location' => 'nullable|string',
        ];
    }
}
