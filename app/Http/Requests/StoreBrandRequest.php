<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreBrandRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // Brand Basic Info
            'name_en' => 'required|string|max:255|unique:brands,name_en',
            'name_ar' => 'nullable|string|max:255|unique:brands,name_ar',
            'slug' => 'required|string|max:255|unique:brands,slug',
            'country_of_origin' => 'nullable|string|max:255',
            'is_trademark_registered' => 'nullable|boolean',
            'website' => 'nullable|url|max:255',
            'instagram' => 'nullable|string|max:255',
            'facebook' => 'nullable|string|max:255',

            // Brand Relationships - Boolean fields
            'manufacturer' => 'nullable|boolean',
            'brand_owner' => 'nullable|boolean',
            'marketing_auth_holder' => 'nullable|boolean',
            'exclusive_distributor' => 'nullable|boolean',
            'authorized_distributor' => 'nullable|boolean',
            'wholesaler' => 'nullable|boolean',
            'authorized_retailer' => 'nullable|boolean',
            'direct_importer' => 'nullable|boolean',
            'parallel_importer' => 'nullable|boolean',
            'drop_shipper' => 'nullable|boolean',

            // Brand Visibility - Integer fields
            'skus_on_brand_website' => 'nullable|integer|min:0',
            'skus_on_amazon' => 'nullable|integer|min:0',
            'skus_on_noon' => 'nullable|integer|min:0',
            'skus_on_other_marketplaces' => 'nullable|integer|min:0',
            'skus_on_own_website' => 'nullable|integer|min:0',

            // Sales Channels - Text fields
            'sold_in_hypermarkets' => 'nullable|string',
            'sold_in_pharmacies' => 'nullable|string',
            'sold_in_specialty_stores' => 'nullable|string',

            // Regulatory Compliance - Enum fields
            'mohap_registration' => 'nullable|in:yes,no,not_required',
            'dubai_municipality_registration' => 'nullable|in:yes,no,not_required',

            // Brand Highlights
            'brand_usp' => 'nullable|string',
            'top_products' => 'nullable|array|max:3',
            'top_products.*' => 'string|max:255',

            // Supporting Documents
            'logo' => 'nullable|string|max:255',
            'trademark_document' => 'nullable|string|max:255',
            'relationship_proof' => 'nullable|string|max:255',
            'purchase_proof' => 'nullable|string|max:255',
            'self_declaration' => 'nullable|string|max:255',
            'product_pictures' => 'nullable|array',
            'product_pictures.*' => 'string|max:255',

            // Status and Activation
            'is_active' => 'nullable|boolean',
            'status' => 'nullable|in:pending,approved,rejected',
        ];
    }
}
