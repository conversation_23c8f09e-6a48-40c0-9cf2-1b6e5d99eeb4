<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreSupportTopicRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'category_id' => 'required|exists:support_categories,id',
            'name' => 'required|string|max:255|unique:support_topics,name',
            'status' => 'required|in:active,inactive',
        ];
    }
}
