<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductClassRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'code' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('product_classes', 'code')->ignore($this->product_class)
            ],
            'is_popular' => 'nullable|boolean',
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'parent_id' => 'nullable|exists:product_classes,id',
            'sub_category_id' => 'nullable|exists:product_classes,id',
            'status' => 'nullable|in:active,inactive',
        ];
    }
}
