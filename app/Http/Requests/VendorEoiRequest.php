<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VendorEoiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_tl_en' => 'required|string|unique:vendor_eoi,name_tl_en',
            'name_tl_ar' => 'required|string',
            'vendor_display_name_en' => 'required|string',
            'vendor_display_name_ar' => 'required|string',
            'website' => 'nullable|url|max:255',
            'instagram_page' => 'nullable|url|max:255',
            'facebook_page' => 'nullable|url|max:255',
            'business_type' => 'required|in:Manufacturer,Importer,Distributor,Retailer,Others',
            'manufacturer_brands' => 'nullable|string',
            'importer_brands' => 'nullable|string',
            'distributor_stores' => 'nullable|string',
            'retailer_total_outlets' => 'nullable|integer|min:0',
            'sku_on_amazon' => 'nullable|integer|min:0',
            'sku_on_noon' => 'nullable|integer|min:0',
            'sku_on_other_marketplaces' => 'nullable|integer|min:0',
            'sku_on_own_website' => 'nullable|integer|min:0',
            'brands_to_sell' => 'nullable|string',
            'categories_to_sell' => 'nullable|string',
            'tl_license_issuing_authority' => 'nullable|string|max:255',
            'tl_license_first_issue_date' => 'nullable|date',
            'tl_license_valid_till' => 'nullable|date',
            'tl_entity_type' => 'nullable|string|max:50',
            'tax_registration_number' => 'nullable|string|max:50',
            'trn_issue_date' => 'nullable|date',
            'trn_name_in_english' => 'nullable|string|max:255',
            'trn_name_in_arabic' => 'nullable|string|max:255',
            'director_name' => 'nullable|string|max:255',
            'director_designation' => 'nullable|string|max:255',
            'director_full_name_passport' => 'nullable|string|max:255',
            'director_passport_number' => 'nullable|string|max:50',
            'director_emirates_id_number' => 'nullable|string|max:50',
            'director_emirates_id_issue_date' => 'nullable|date',
            'director_emirates_id_expiry_date' => 'nullable|date',
            'director_email' => 'nullable|email|max:255',
            'director_mobile' => 'nullable|string|max:20',
            'director_preferred_language' => 'nullable|string|max:50',
            'spoc_name' => 'nullable|string|max:255',
            'spoc_designation' => 'nullable|string|max:255',
            'spoc_email' => 'required|nullable|email|max:255',
            'spoc_mobile' => 'nullable|string|max:20',
            'spoc_passport_number' => 'nullable|string|max:50',
            'spoc_emirates_id_number' => 'nullable|string|max:50',
            'spoc_emirates_id_issue_date' => 'nullable|date',
            'spoc_emirates_id_expiry_date' => 'nullable|date',
            'spoc_letter_of_authorization' => 'nullable|string',
            'additional_info' => 'nullable|string',
            'approval_status' => 'in:Pending,Approved,Rejected,OnHold,Cancelled',
            'approved_by' => 'nullable|integer',
            'is_active' => 'boolean',
        ];
    }
}
