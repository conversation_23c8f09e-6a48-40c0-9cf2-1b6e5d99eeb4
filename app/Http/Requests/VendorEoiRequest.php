<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class VendorEoiRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_tl_en' => 'required|string|unique:vendor_eoi,name_tl_en',
            'name_tl_ar' => 'required|string',
            'vendor_display_name_en' => 'required|string',
            'vendor_display_name_ar' => 'required|string',
            'website' => 'nullable|url|max:255',
            'instagram_page' => 'nullable|url|max:255',
            'facebook_page' => 'nullable|url|max:255',
            'other_social_media' => 'nullable|url|max:255',
            'business_data' => 'nullable|array',
            'business_data.*.type' => 'required_with:business_data|in:Manufacturer,Importer,Distributor,Retailer,Others',
            'business_data.*.details' => 'nullable|string|max:500',
            'sku_on_amazon' => 'nullable|integer|min:0',
            'sku_on_noon' => 'nullable|integer|min:0',
            'sku_on_other_marketplaces' => 'nullable|integer|min:0',
            'sku_on_own_website' => 'nullable|integer|min:0',
            'brands_to_sell' => 'nullable|string',
            'categories_to_sell' => 'nullable|array',
            'categories_to_sell.*' => 'integer|exists:categories,id',
            'inventory_management' => 'nullable|in:store_inventory,manage_orders,both',
            'order_collection_location' => 'nullable|array',
            'order_collection_location.*' => 'string|max:255',
            'order_collection_location_details' => 'nullable|string|max:1000',
            'tl_license_issuing_authority' => 'nullable|string|max:255',
            'tl_license_first_issue_date' => 'nullable|date',
            'tl_license_valid_till' => 'nullable|date',
            'tl_entity_type' => 'nullable|in:LLC,PJSC,Sole_Proprietorship,Partnership,Branch',
            'spoc_name' => 'nullable|string|max:255',
            'spoc_designation' => 'nullable|string|max:255',
            'spoc_email' => 'required|email|max:255',
            'spoc_mobile' => 'nullable|string|max:20',
            'additional_info' => 'nullable|string',
        ];
    }
}
