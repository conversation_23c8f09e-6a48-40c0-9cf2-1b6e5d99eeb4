<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreVendorEoiBankInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'vendor_eoi_id' => 'required|exists:vendor_eoi,id',
            'bank_name' => 'required|nullable|string|max:255',
            'branch_name' => 'nullable|string|max:255',
            'account_holder_name' => 'required|nullable|string|max:255',
            'iban_number' => 'required|nullable|string|max:255|unique:vendor_eoi_bank_information,iban_number',
            'original_cheque_number' => 'nullable|string|max:255',
            'is_active' => 'boolean'
        ];
    }
}
