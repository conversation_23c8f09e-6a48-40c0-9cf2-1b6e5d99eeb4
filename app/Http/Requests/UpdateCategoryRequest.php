<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCategoryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $categoryId = $this->route('category');
        return   [
            'code'             => 'nullable|string|max:255|unique:categories,code,' . $categoryId,
            'fee_text'         => 'nullable|string|max:255',
            'name'             => 'required|string|max:255',
            'name_ar'             => 'nullable|string|max:255',
            'type'             => 'required|in:main,sub',
            'parent_id'        => 'nullable|exists:categories,id',
            'ordering_number'  => 'nullable|integer',
            'banner'           => 'nullable|string',
            'icon'             => 'nullable|string',
            'cover_image'      => 'nullable|string',
            'meta_title'       => 'nullable|string|max:255',
            'meta_description' => 'nullable|string',
            'slug'             => 'required|string|max:255|unique:categories,slug,' . $categoryId,
            // '_method'          => 'required|in:PUT,PATCH',
        ];
    }
}
