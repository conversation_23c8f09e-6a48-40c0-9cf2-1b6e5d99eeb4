<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Ownership & Categorization
            'vendor_id' => 'sometimes|required|exists:vendors,id',
            'category_id' => 'sometimes|required|exists:categories,id',
            'sub_category_id' => 'nullable|exists:categories,id',
            'class_id' => 'nullable|exists:product_classes,id',
            'sub_class_id' => 'nullable|exists:product_classes,id',

            // Product Identity & Basic Info
            'vendor_sku' => 'nullable|string|max:255|unique:products,vendor_sku',
            'barcode' => 'nullable|string|max:255',
            'model_number' => 'nullable|string|max:255',
            'brand_id' => 'nullable|integer',

            // Multi-language Product Titles & Descriptions
            'title_en' => 'sometimes|required|string|max:255',
            'title_ar' => 'nullable|string|max:255',
            'short_name' => 'nullable|string|max:255',
            'short_description_en' => 'nullable|string',
            'short_description_ar' => 'nullable|string',
            'description_en' => 'nullable|string',
            'description_ar' => 'nullable|string',

            // Product Details & Specifications
            'key_ingredients' => 'nullable|string',
            'usage_instructions' => 'nullable|string',
            'user_group' => 'nullable|string|max:255',
            'net_weight' => 'nullable|string|max:255',
            'net_weight_unit' => 'nullable|string|max:255',
            'formulation' => 'nullable|string|max:255',
            'servings' => 'nullable|integer',
            'flavour' => 'nullable|string|max:255',
            'is_variant' => 'nullable|boolean',

            // Pricing & Promotional Info
            'regular_price' => 'sometimes|required|decimal:0,2|min:0|max:999999.99',
            'offer_price' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'vat_tax' => 'nullable|in:exempted,standard_5,zero_rated',
            'discount_start_date' => 'nullable|date',
            'discount_end_date' => 'nullable|date|after:discount_start_date',
            'approx_commission' => 'nullable|decimal:0,2|min:0|max:999999.99',

            // Compliance & Legal Information
            'country_of_origin' => 'nullable|string|max:255',
            'bbe_date' => 'nullable|date',
            'regulatory_product_registration' => 'nullable|string|max:255',
            'vat_tax_url' => 'nullable|string|max:255',
            'is_vegan' => 'nullable|boolean',
            'is_vegetarian' => 'nullable|boolean',
            'is_halal' => 'nullable|boolean',
            'allergen_info' => 'nullable|string',
            'storage_conditions' => 'nullable|string',
            'dietary_needs' => 'nullable|string|max:255',

            // Packaging / Shipping Dimensions
            'package_length' => 'nullable|decimal:0,2|min:0|max:9999.99',
            'package_width' => 'nullable|decimal:0,2|min:0|max:9999.99',
            'package_height' => 'nullable|decimal:0,2|min:0|max:9999.99',
            'package_weight' => 'nullable|decimal:0,2|min:0|max:9999.99',

            // Status & Control
            'is_active' => 'nullable|in:0,1',
            'is_approved' => 'nullable|in:0,1',
            'status' => 'nullable|in:draft,pending,submitted',

            // product seo

            // product seo
            'product_seo' => 'sometimes|nullable|array',
            'product_seo.meta_title_en' => 'nullable|string|max:255',
            'product_seo.meta_description_en' => 'nullable|string|max:255',
            'product_seo.keywords_en' => 'nullable|string|max:255',
            'product_seo.meta_title_ar' => 'nullable|string|max:255',
            'product_seo.meta_description_ar' => 'nullable|string|max:255',
            'product_seo.keywords_ar' => 'nullable|string|max:255',

            // product faqs
            'product_faqs' => 'sometimes|nullable|array',
            'product_faqs.*.question' => 'required|string|max:255',
            'product_faqs.*.answer' => 'required|string',

            // Fulfillment & Shipping
            'product_fulfillment' => 'sometimes|nullable|array',
            'product_fulfillment.mode' => 'nullable|in:fba,drop_ship,self_ship',
            'product_fulfillment.is_returnable' => 'nullable|boolean',
            'product_fulfillment.collection_point' => 'nullable|string|max:255',
            'product_fulfillment.shipping_time' => 'nullable|integer|min:0',
            'product_fulfillment.shipping_fee' => 'nullable|decimal:0,2|min:0|max:999999.99',

            //product variants
            'product_variants' => 'sometimes|nullable|array',
            'product_variants.*.regular_price' => 'required|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.offer_price' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.vat_tax' => 'nullable|in:exempted,standard_5,zero_rated',
            'product_variants.*.discount_start_date' => 'nullable|date',
            'product_variants.*.discount_end_date' => 'nullable|date|after:discount_start_date',
            'product_variants.*.stock' => 'nullable|integer|min:0',
            'product_variants.*.sku' => 'nullable|string|max:255',
            'product_variants.*.barcode' => 'nullable|string|max:255',
            'product_variants.*.weight' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.length' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.width' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.height' => 'nullable|decimal:0,2|min:0|max:999999.99',
            'product_variants.*.path' => 'nullable|string|max:255',
            'product_variants.*.is_active' => 'nullable|boolean',
            'product_variants.*.attribute_id' => 'nullable|integer|exists:product_attributes,id',
            'product_variants.*.attribute_value_id' => 'nullable|integer|exists:product_attribute_values,id',

        ];
    }
}
