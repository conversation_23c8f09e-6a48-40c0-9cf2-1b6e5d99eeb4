<?php

namespace App\Http\Controllers;

use App\Services\HomeService;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class HomeController extends Controller
{
    use HelperTrait;

    private $homeService;

    public function __construct(HomeService $homeService)
    {
        $this->homeService = $homeService;
    }

    /**
     * Get home page data
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getHomePageData($request);

            return $this->successResponse($data, 'Home page data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get featured/popular products
     */
    public function featuredProducts(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getFeaturedProducts($request);

            return $this->successResponse($data, 'Featured products retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get latest products
     */
    public function latestProducts(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getLatestProducts($request);

            return $this->successResponse($data, 'Latest products retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get categories with product counts
     */
    public function categories(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getCategoriesWithProductCounts($request);

            return $this->successResponse($data, 'Categories retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get promotional banners
     */
    public function banners(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getPromotionalBanners($request);

            return $this->successResponse($data, 'Banners retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get best sellers by category
     */
    public function bestSellers(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getBestSellersByCategory($request);

            return $this->successResponse($data, 'Best sellers retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get product recommendations
     */
    public function recommendations(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getProductRecommendations($request);

            return $this->successResponse($data, 'Product recommendations retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get popular brands
     */
    public function popularBrands(Request $request): JsonResponse
    {
        try {
            $data = $this->homeService->getPopularBrands($request);

            return $this->successResponse($data, 'Popular brands retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
