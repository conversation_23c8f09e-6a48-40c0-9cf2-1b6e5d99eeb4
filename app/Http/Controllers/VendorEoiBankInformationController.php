<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreVendorEoiBankInfoRequest;
use App\Services\VendorEoiBankInformationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

use App\Traits\HelperTrait;

class VendorEoiBankInformationController extends Controller
{
    
    use HelperTrait;
    protected $VendorEoiBankInformationService;

    public function __construct(VendorEoiBankInformationService $service)
    {
        $this->VendorEoiBankInformationService = $service;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->VendorEoiBankInformationService->index($request);

            return $this->successResponse($data, 'Vendor Bank data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(StoreVendorEoiBankInfoRequest $request): JsonResponse
    {
        try {
            $resource = $this->VendorEoiBankInformationService->store($request);

            return $this->successResponse($resource, 'Vendor Bank information created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Vendor Bank Information', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getEoiBankList(Request $request, $vendor_eoi_id): JsonResponse
    {
        try {
            $data = $this->VendorEoiBankInformationService->getEoiBankList($request, $vendor_eoi_id);

            return $this->successResponse($data, 'Vendor Bank information data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
