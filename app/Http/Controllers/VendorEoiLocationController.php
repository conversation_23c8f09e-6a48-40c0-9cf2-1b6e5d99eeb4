<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Requests\StoreVendorEoiLocationRequest;
use App\Services\VendorEoiLocationService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

use App\Traits\HelperTrait;

class VendorEoiLocationController extends Controller
{
     use HelperTrait;
    protected $VendorEoiLocationService;


    public function __construct(VendorEoiLocationService $service)
    {
        $this->VendorEoiLocationService = $service;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->VendorEoiLocationService->index($request);

            return $this->successResponse($data, 'Location data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(StoreVendorEoiLocationRequest $request): JsonResponse
    {
        try {
            $resource = $this->VendorEoiLocationService->store($request);

            return $this->successResponse($resource, 'Location information created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Location', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getEoiLocationList(Request $request, $vendor_eoi_id): JsonResponse
    {
        try {
            $data = $this->VendorEoiLocationService->locationList($request, $vendor_eoi_id);

            return $this->successResponse($data, 'Location data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

}
