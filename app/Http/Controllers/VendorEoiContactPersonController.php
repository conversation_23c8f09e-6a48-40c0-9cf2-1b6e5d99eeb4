<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\VendorEoiContactPerson;
use App\Http\Requests\StoreVendorEoiContactPersonRequest;
use App\Services\VendorEoiContactPersonService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

use App\Traits\HelperTrait;

class VendorEoiContactPersonController extends Controller
{
    use HelperTrait;
    protected $VendorEoiContactPersonService;

    public function __construct(VendorEoiContactPersonService $service)
    {
        $this->VendorEoiContactPersonService = $service;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $data = $this->VendorEoiContactPersonService->index($request);

            return $this->successResponse($data, 'Contact Person data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function store(StoreVendorEoiContactPersonRequest $request): JsonResponse
    {
        try {
            $resource = $this->VendorEoiContactPersonService->store($request);

            return $this->successResponse($resource, 'Contact Person information created successfully!', Response::HTTP_CREATED);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), $e->getMessage(), Response::HTTP_UNPROCESSABLE_ENTITY);
        }
         catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to create Contact Person', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function getEoiContactPersonList(Request $request, $vendor_eoi_id): JsonResponse
    {
        try {
            $data = $this->VendorEoiContactPersonService->contactPersonList($request, $vendor_eoi_id);

            return $this->successResponse($data, 'Contact Person data retrieved successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Something went wrong', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
