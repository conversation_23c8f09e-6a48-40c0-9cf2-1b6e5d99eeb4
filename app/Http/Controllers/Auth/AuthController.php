<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\AvatarUpdateRequest;
use App\Http\Requests\ChangePasswordRequest;
use App\Http\Requests\ForgotPasswordRequest;
use App\Http\Requests\LoginRequest;
use App\Http\Requests\RefreshTokenRequest;
use App\Http\Requests\RegisterRequest;
use App\Http\Requests\ResendOtpRequest;
use App\Http\Requests\ResetPasswordRequest;
use App\Http\Requests\UpdateProfileRequest;
use App\Http\Requests\UpdateUserRequest;
use App\Http\Requests\VerifyOtpRequest;
use App\Mail\OtpVerificationMail;
use App\Mail\ResetPasswordMail;
use App\Models\OtpVerification;
use App\Models\User;
use App\Traits\HelperTrait;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class AuthController extends Controller
{
    use HelperTrait;

    /**
     * User registration
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $userData = $request->validated();

            $plainPassword = $userData['password']; // Save plain password for token request
            $userData['password'] = bcrypt($plainPassword); // Hash for DB

            $user = User::create($userData);
            $user->assignRole('customer');

            $user->customer()->create([
                'gender' => $userData['gender'] ?? null,
                'date_of_birth' => $userData['date_of_birth'] ?? null,
                'customer_type' => $userData['customer_type'] ?? null,
            ]);

            // Generate OTP
            $otp = $this->generateOTP();

            // Store OTP in database
            OtpVerification::create([
                'user_id' => $user->id,
                'code' => $otp,
                'expires_at' => Carbon::now()->addMinutes(10), // OTP expires in 10 minutes
            ]);

            // Send OTP via email if email is provided
            if ($user->email) {
                Mail::to($user->email)->send(new OtpVerificationMail($user, $otp));
            }

            // Create token request with the plain password
            $tokenRequest = Request::create('/oauth/token', 'POST', [
                'grant_type' => 'password',
                'client_id' => config('app.passport.password_client_id'),
                'client_secret' => config('app.passport.password_client_secret'),
                'username' => $userData['email'],
                'password' => $plainPassword,
                'scope' => '',
            ]);

            $response = app()->handle($tokenRequest);
            $token = json_decode($response->getContent(), true);

            $user['token'] = $token;
            DB::commit();

            return $this->successResponse(
                $user,
                'Registration successful! An OTP has been sent to your provided contact.',
                201
            );
        } catch (\Throwable $th) {
            DB::rollBack();
            return $this->errorResponse($th->getMessage(), 'Failed to register user', 500);
        }
    }

    /**
     * Generate a random 6-digit OTP
     */
    private function generateOTP(): string
    {
        return str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
    }


    /**
     * Login user
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $identifier = $validated['email_or_phone'];
        $isEmail = filter_var($identifier, FILTER_VALIDATE_EMAIL);

        $credentials = [
            $isEmail ? 'email' : 'phone' => $identifier,
            'password' => $validated['password'],
        ];

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            if (!$user->is_verified) {
                Auth::logout();
                return $this->errorResponse(
                    'Your account is not verified. Please check your inbox for the OTP or verification link.',
                    'Account not verified',
                    403
                );
            }

            // if (!$user->is_active || $user->status !== User::ACTIVE) {
            //     Auth::logout();
            //     return $this->errorResponse(
            //         'Your account is not active. Please contact support.',
            //         'Account not active',
            //         403
            //     );
            // }


            $tokenRequest = Request::create('/oauth/token', 'POST', [
                'grant_type'    => 'password',
                'client_id' => config('app.passport.password_client_id'),
                'client_secret' => config('app.passport.password_client_secret'),
                'username'      => $identifier,
                'password'      => $validated['password'],
                'scope'         => '',
            ]);

            $response = app()->handle($tokenRequest);
            $tokenContent = json_decode($response->getContent(), true);

            $role = $user->getRoleNames()->first();
            $roles = $user->getRoleNames()->toArray();
            $permissions = $user->getAllPermissions()->pluck('name');

            $user = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'is_verified' => $user->is_verified,
            ];

            $res = [
                'user' => $user,
                'role' => $role,
                'roles' => $roles,
                'permissions' => $permissions,
                'token' => $tokenContent,
            ];

            return $this->successResponse($res, 'User logged in successfully');
        }

        return $this->errorResponse('Invalid credentials', 'Authentication failed', 401);
    }


    /**
     * admin Login user
     */

    public function adminLogin(LoginRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $identifier = $validated['email_or_phone'];
        $isEmail = filter_var($identifier, FILTER_VALIDATE_EMAIL);

        $credentials = [
            $isEmail ? 'email' : 'phone' => $identifier,
            'password' => $validated['password'],
        ];

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            if (!$user->is_verified) {
                Auth::logout();
                return $this->errorResponse(
                    'Your account is not verified. Please check your inbox for the OTP or verification link.',
                    'Account not verified',
                    403
                );
            }

            if (!$user->hasRole('admin')) {
                Auth::logout();
                return $this->errorResponse(

                    'You are not authorized to access this resource.',
                    'Unauthorized',
                    403
                );
            }

            $tokenRequest = Request::create('/oauth/token', 'POST', [
                'grant_type'    => 'password',
                'client_id'     => config('app.passport.password_client_id'),
                'client_secret' => config('app.passport.password_client_secret'),
                'username'      => $identifier,
                'password'      => $validated['password'],
                'scope'         => '',
            ]);

            $response = app()->handle($tokenRequest);
            $tokenContent = json_decode($response->getContent(), true);
            $role = $user->getRoleNames()->first();
            $roles = $user->getRoleNames()->toArray();
            $permissions = $user->getAllPermissions()->pluck('name');



            $user = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'phone' => $user->phone,
                'avatar' => $user->avatar,
                'is_verified' => $user->is_verified,
            ];

            $res = [
                'user' => $user,
                'role' => $role,
                'roles' => $roles,
                'token' => $tokenContent,
                'permissions' => $permissions,
            ];


            return $this->successResponse($res, 'User logged in successfully');
        }

        return $this->errorResponse('Invalid credentials', 'Authentication failed', 401);
    }


    /**
     * Login user
     *
     * @param  LoginRequest  $request
     */
    public function me(): JsonResponse
    {
        $user = auth()->user()->loadMissing(['roles', 'permissions']);
        $userData = $user->toArray();
        $userData['roles'] = $user->roles->pluck('name');
        $userData['permissions'] = $user->getAllPermissions()->pluck('name');

        return $this->successResponse($userData, 'User data retrieved successfully', 200);
    }

    /**
     * refresh token
     */
    public function refreshToken(RefreshTokenRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $response = Http::asForm()->post(env('APP_URL') . '/oauth/token', [
            'grant_type' => 'refresh_token',
            'refresh_token' => $validated['refresh_token'],
            'client_id' => config('app.passport.password_client_id'),
            'client_secret' => config('app.passport.password_client_secret'),
            'scope' => '',
        ]);

        return $this->successResponse($response->json(), 'Token refreshed successfully', 200);
    }

    /**
     * Verify OTP
     */
    public function verifyOtp(VerifyOtpRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Find user by email or phone
        $user = null;
        if (isset($validated['email'])) {
            $user = User::where('email', $validated['email'])->first();
        } elseif (isset($validated['phone'])) {
            $user = User::where('phone', $validated['phone'])->first();
        }

        if (!$user) {
            return $this->errorResponse('User not found', 'Verification failed', 404);
        }

        // Check if user is already verified
        if ($user->is_verified) {
            return $this->successResponse(null, 'Account is already verified', 200);
        }

        // Find the latest OTP for this user
        $otpVerification = OtpVerification::where('user_id', $user->id)
            ->where('code', $validated['otp'])
            ->where('expires_at', '>', Carbon::now())
            ->latest()
            ->first();

        if (!$otpVerification) {
            return $this->errorResponse('Invalid or expired OTP', 'Verification failed', 400);
        }

        // Mark user as verified
        $user->is_verified = true;
        $user->email_verified_at = Carbon::now();
        $user->save();

        // Delete used OTP
        $otpVerification->delete();

        return $this->successResponse(null, 'Account verified successfully', 200);
    }

    /**
     * Resend OTP
     */
    public function resendOtp(ResendOtpRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Find user by email or phone
        $user = null;
        if (isset($validated['email'])) {
            $user = User::where('email', $validated['email'])->first();
        } elseif (isset($validated['phone'])) {
            $user = User::where('phone', $validated['phone'])->first();
        }

        if (!$user) {
            return $this->errorResponse('User not found', 'OTP resend failed', 404);
        }

        // Check if user is already verified
        if ($user->is_verified) {
            return $this->errorResponse('Account is already verified', 'OTP resend not needed', 400);
        }

        // Delete any existing OTPs for this user
        OtpVerification::where('user_id', $user->id)->delete();

        // Generate new OTP
        $otp = $this->generateOTP();

        // Store OTP in database
        OtpVerification::create([
            'user_id' => $user->id,
            'code' => $otp,
            'expires_at' => Carbon::now()->addMinutes(10), // OTP expires in 10 minutes
        ]);

        // Send OTP via email if email is provided
        if ($user->email) {
            Mail::to($user->email)->send(new OtpVerificationMail($user, $otp));
        }

        return $this->successResponse(null, 'OTP has been resent to your provided contact', 200);
    }

    /**
     * Logout
     */
    public function logout(): JsonResponse
    {
        Auth::user()->tokens()->delete();

        return $this->successResponse(null, 'User logged out successfully', 200);
    }

    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $user = Auth::user();

        if (!Hash::check($validated['old_password'], $user->password)) {
            return $this->errorResponse('Old password is incorrect', 'Password change failed', 400);
        }

        $user->password = bcrypt($validated['new_password']);
        $user->save();

        return $this->successResponse(null, 'Password changed successfully', 200);
    }

    public function updateProfile(UpdateProfileRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $user = Auth::user();

        $user->update($validated);

        return $this->successResponse(null, 'Profile updated successfully', 200);
    }

    public function changeAvatar(AvatarUpdateRequest $request): JsonResponse
    {
        $avatar = $this->s3FileUpload($request, 'avatar', 'avatar');

        User::where('id', auth()->user()->id)->update(['avatar' => $avatar['path']]);

        return $this->successResponse(null, 'Avatar updated successfully', 200);
    }

    /**
     * Send a reset link to the given user.
     */
    public function forgotPassword(ForgotPasswordRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Find the user by email
        $user = User::where('email', $validated['email'])->first();

        // Generate a random token
        $token = Str::random(60);

        // Store the token in the password_reset_tokens table
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $validated['email']],
            [
                'token' => $token,
                'created_at' => Carbon::now()
            ]
        );

        // Send the reset link email
        Mail::to($user->email)->send(new ResetPasswordMail($user, $token));

        return $this->successResponse(null, 'Password reset link has been sent to your email address', 200);
    }

    /**
     * Reset the given user's password.
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        $validated = $request->validated();

        // Find the token in the database
        $tokenData = DB::table('password_reset_tokens')
            ->where('email', $validated['email'])
            ->where('token', $validated['token'])
            ->first();

        // Check if token exists and is not expired (60 minutes)
        if (!$tokenData || Carbon::parse($tokenData->created_at)->addMinutes(60)->isPast()) {
            return $this->errorResponse('Invalid or expired token', 'Password reset failed', 400);
        }

        // Find the user and update the password
        $user = User::where('email', $validated['email'])->first();
        $user->password = bcrypt($validated['password']);
        $user->save();

        // Delete the token
        DB::table('password_reset_tokens')->where('email', $validated['email'])->delete();

        return $this->successResponse(null, 'Password has been reset successfully', 200);
    }
}
