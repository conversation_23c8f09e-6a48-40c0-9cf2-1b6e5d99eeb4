<?php

namespace App\Http\Controllers;

use App\Http\Requests\FileUploadRequest;
use App\Http\Requests\MultipleFileUploadRequest;
use App\Traits\HelperTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CommonController extends Controller
{
    use HelperTrait;

    public function fileUpload(FileUploadRequest $request): JsonResponse
    {
        try {
            $uploadResult = $this->s3FileUpload($request, 'file', 'uploads');
            $uploadResult['url'] = config('filesystems.disks.s3.url') . '/' . $uploadResult['path'];
            return $this->successResponse($uploadResult, 'File uploaded successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to upload file', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Upload multiple files to S3
     */
    public function multipleFileUpload(MultipleFileUploadRequest $request): JsonResponse
    {
        try {
            $uploadResults = $this->s3MultipleFileUpload($request, 'files', 'uploads');
            foreach ($uploadResults as $key => $value) {
                $uploadResults[$key]['url'] = config('filesystems.disks.s3.url') . '/' . $value['path'];
            }
            return $this->successResponse($uploadResults, 'Files uploaded successfully!', Response::HTTP_OK);
        } catch (\Throwable $th) {
            return $this->errorResponse($th->getMessage(), 'Failed to upload files', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
