<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

use App\Models\VendorEoi;

class VendorEOISubmission extends Mailable
{
    use Queueable, SerializesModels;

    public $vendor;

    public function __construct(VendorEOI $vendor)
    {
        $this->vendor = $vendor;
    }

    public function build()
    {
        return $this->subject('New Vendor EOI Submission')->view('emails.vendor_eoi_submitted');
    }
}
