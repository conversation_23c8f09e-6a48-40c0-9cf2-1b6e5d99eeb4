<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorEoiContactPerson extends Model
{
    protected $table = 'vendor_eoi_contact_persons';

    protected $fillable = [
        'vendor_eoi_id',
        'type',
        'full_name',
        'designation',
        'email',
        'mobile_number',
    ];

    public function vendor()
    {
        return $this->belongsTo(VendorEoi::class, 'vendor_eoi_id');
    }
}
