<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    protected $fillable = [
        'user_id',
        'vendor_id',
        'category_id',
        'sub_category_id',
        'class_id',
        'sub_class_id',
        'vendor_sku',
        'system_sku',
        'barcode',
        'model_number',
        'brand_id',
        'title_en',
        'title_ar',
        'short_name',
        'short_description_en',
        'short_description_ar',
        'description_en',
        'description_ar',
        'key_ingredients',
        'usage_instructions',
        'user_group',
        'net_weight',
        'net_weight_unit',
        'formulation',
        'servings',
        'flavour',
        'is_variant',
        'regular_price',
        'offer_price',
        'vat_tax',
        'discount_start_date',
        'discount_end_date',
        'approx_commission',
        'country_of_origin',
        'bbe_date',
        'regulatory_product_registration',
        'vat_tax_utl',
        'is_vegan',
        'is_vegetarian',
        'is_halal',
        'allergen_info',
        'storage_conditions',
        'package_length',
        'package_width',
        'package_height',
        'package_weight',
        'is_active',
        'is_approved',
        'status',
        'submission_status',
        'brand_code',
        'product_code',
        'size_code',
        'dietary_needs'
    ];

    protected $casts = [
        'is_variant' => 'boolean',
        'is_vegan' => 'boolean',
        'is_vegetarian' => 'boolean',
        'is_halal' => 'boolean',
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
        'discount_start_date' => 'datetime',
        'discount_end_date' => 'datetime',
        'bbe_date' => 'date',
        'regular_price' => 'decimal:2',
        'offer_price' => 'decimal:2',
        'approx_commission' => 'decimal:2',
        'package_length' => 'decimal:2',
        'package_width' => 'decimal:2',
        'package_height' => 'decimal:2',
        'package_weight' => 'decimal:2'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function productClass()
    {
        return $this->belongsTo(ProductClass::class, 'class_id');
    }

    public function subCategory()
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function subClass()
    {
        return $this->belongsTo(ProductClass::class, 'sub_class_id');
    }

    public function productMedia()
    {
        return $this->hasMany(ProductMedia::class);
    }

    public function productSeo()
    {
        return $this->hasOne(ProductSeo::class);
    }

    public function productFaqs()
    {
        return $this->hasMany(ProductFaq::class);
    }

    public function fulfillment()
    {
        return $this->hasOne(ProductFulfillment::class);
    }

    /**
     * Boot method to automatically generate system_sku
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->system_sku)) {
                $product->system_sku = 'SKU-' . uniqid();
            }
        });
    }

    public function productVariants()
    {
        return $this->hasMany(ProductVariant::class);
    }
}
