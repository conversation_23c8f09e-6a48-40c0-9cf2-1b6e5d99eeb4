<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Category extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'type',
        'code',
        'fee_text',
        'parent_id',
        'ordering_number',
        'banner',
        'icon',
        'cover_image',
        'meta_title',
        'meta_description',
        'slug',
        'status',
    ];

    protected $casts = [
        'filtering_attributes' => 'array',
    ];

    protected $appends = [
        'banner_url',
        'cover_image_url',
        'icon_url',
    ];

    public function parent(): BelongsTo
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function getBannerAttribute($value)
    {
        return $value;
    }

    public function getCoverImageAttribute($value)
    {
        return $value;
    }

    public function getIconAttribute($value)
    {
        return $value;
    }

    public function getBannerUrlAttribute()
    {
        if ($this->banner) {
            return config('filesystems.disks.s3.url') . '/' . $this->banner;
        }
        return null;
    }

    public function getCoverImageUrlAttribute()
    {
        if ($this->cover_image) {
            return config('filesystems.disks.s3.url') . '/' . $this->cover_image;
        }
        return null;
    }

    public function getIconUrlAttribute()
    {
        if ($this->icon) {
            return config('filesystems.disks.s3.url') . '/' . $this->icon;
        }
        return null;
    }
}
