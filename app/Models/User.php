<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Support\Str;
use Laravel\Passport\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasRoles;

    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'uid',
        'is_verified',
        'status',
        'is_active',
    ];

    const PENDING = 'pending';
    const ACTIVE = 'active';
    const INACTIVE = 'inactive';
    const BANNED = 'banned';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uid)) {
                $user->uid = (string) Str::uuid();
            }
        });
    }

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $appends = [
        'avatar_url',
    ];

    protected function getDefaultGuardName(): string
    {
        return 'api';
    }

    public function customer()
    {
        return $this->hasOne(Customer::class);
    }

    public function getAvatarAttribute($value)
    {
        return $value;
    }

    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return config('filesystems.disks.s3.url') . '/' . $this->avatar;
        }
        return null;
    }
}
