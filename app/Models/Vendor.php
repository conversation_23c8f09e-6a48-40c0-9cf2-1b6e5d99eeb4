<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'legal_name_en',
        'legal_name_ar',
        'display_name_en',
        'display_name_ar',
        'business_type',
        'license_issuing_authority',
        'license_issue_date',
        'license_renewal_date',
        'license_valid_till',
        'license_entity_type',
        'number_of_partners',
        'trade_license_copy',
        'bank_name',
        'bank_branch',
        'account_holder_name',
        'iban',
        'iban_cert_copy',
        'cheque_number',
        'tax_registration_number',
        'tax_issue_date',
        'tax_name_ar',
        'tax_name_en',
        'vat_cert_copy',
        'director_name_tl',
        'director_designation_tl',
        'director_name_passport',
        'director_passport_number',
        'director_emirates_id',
        'director_emirates_id_issue',
        'director_emirates_id_expiry',
        'director_email',
        'director_mobile',
        'preferred_language',
        'director_documents',
        'spoc_name',
        'spoc_designation',
        'spoc_email',
        'spoc_mobile',
        'spoc_passport_number',
        'spoc_emirates_id',
        'spoc_emirates_id_issue',
        'spoc_emirates_id_expiry',
        'loa_validity',
        'spoc_documents',
        'self_declaration_signed_by'
    ];

    protected $casts = [
        'license_issue_date' => 'date',
        'license_renewal_date' => 'date',
        'license_valid_till' => 'date',
        'tax_issue_date' => 'date',
        'director_emirates_id_issue' => 'date',
        'director_emirates_id_expiry' => 'date',
        'spoc_emirates_id_issue' => 'date',
        'spoc_emirates_id_expiry' => 'date',
    ];

    /**
     * Get the products for this vendor.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }
}
