<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\User;

class VendorEoi extends Model
{
    use SoftDeletes;

    protected $table = 'vendor_eoi';

    protected $fillable = [
        'veoi_id', 'name_tl_en', 'name_tl_ar', 'vendor_display_name_en', 'vendor_display_name_ar',
        'website', 'instagram_page', 'facebook_page', 'other_social_media', 'business_data',
        'sku_on_amazon', 'sku_on_noon', 'sku_on_other_marketplaces', 'sku_on_own_website',
        'brands_to_sell', 'categories_to_sell', 'inventory_management', 'order_collection_location',
        'order_collection_location_details', 'tl_license_issuing_authority',
        'tl_license_first_issue_date', 'tl_license_valid_till', 'tl_entity_type',
        'spoc_name', 'spoc_designation', 'spoc_email', 'spoc_mobile',
        'additional_info', 'approval_status', 'approved_by', 'is_active'
    ];

    protected $casts = [
        'business_data' => 'array',
        'order_collection_location' => 'array',
        'tl_license_first_issue_date' => 'date',
        'tl_license_valid_till' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who approved this EOI.
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }
}
