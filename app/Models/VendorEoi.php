<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VendorEoi extends Model
{
    use SoftDeletes;

    protected $table = 'vendor_eoi';

    protected $fillable = [
        'veoi_id', 'name_tl_en', 'name_tl_ar', 'vendor_display_name_en', 'vendor_display_name_ar',
        'website', 'instagram_page', 'facebook_page', 'business_type', 'manufacturer_brands',
        'importer_brands', 'distributor_stores', 'retailer_total_outlets',
        'sku_on_amazon', 'sku_on_noon', 'sku_on_other_marketplaces', 'sku_on_own_website',
        'brands_to_sell', 'categories_to_sell', 'tl_license_issuing_authority',
        'tl_license_first_issue_date', 'tl_license_valid_till', 'tl_entity_type',
        'tax_registration_number', 'trn_issue_date', 'trn_name_in_english', 'trn_name_in_arabic',
        'director_name', 'director_designation', 'director_full_name_passport', 'director_passport_number',
        'director_emirates_id_number', 'director_emirates_id_issue_date', 'director_emirates_id_expiry_date',
        'director_email', 'director_mobile', 'director_preferred_language',
        'director_passport_copy', 'director_emirates_id_copy', 'spoc_name',
        'spoc_designation', 'spoc_email', 'spoc_mobile', 'spoc_passport_number',
        'spoc_emirates_id_number', 'spoc_emirates_id_issue_date', 'spoc_emirates_id_expiry_date',
        'spoc_letter_of_authorization', 'spoc_passport_copy', 'spoc_emirates_id_copy', 'spoc_loa_copy',
        'additional_info', 'approval_status', 'approved_by', 'is_active'
    ];
}
