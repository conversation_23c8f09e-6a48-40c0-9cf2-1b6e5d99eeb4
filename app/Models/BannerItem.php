<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BannerItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'banner_id',
        'media_path',
        'link_url',
        'target',
        'alt_text',
        'position',
        'is_active',
        'user_id',
    ];

    protected $casts = [
        'banner_id' => 'integer',
        'position' => 'integer',
        'is_active' => 'boolean',
    ];

    public function banner()
    {
        return $this->belongsTo(Banner::class);
    }
}
