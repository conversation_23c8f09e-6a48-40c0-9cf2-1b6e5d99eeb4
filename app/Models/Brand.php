<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Brand extends Model
{
    use HasFactory;

    protected $fillable = [
        // Brand Basic Info
        'name_en',
        'name_ar',
        'slug',
        'country_of_origin',
        'is_trademark_registered',
        'website',
        'instagram',
        'facebook',

        // Brand Relationships
        'manufacturer',
        'brand_owner',
        'marketing_auth_holder',
        'exclusive_distributor',
        'authorized_distributor',
        'wholesaler',
        'authorized_retailer',
        'direct_importer',
        'parallel_importer',
        'drop_shipper',

        // Brand Visibility
        'skus_on_brand_website',
        'skus_on_amazon',
        'skus_on_noon',
        'skus_on_other_marketplaces',
        'skus_on_own_website',

        // Sales Channels
        'sold_in_hypermarkets',
        'sold_in_pharmacies',
        'sold_in_specialty_stores',

        // Regulatory Compliance
        'mohap_registration',
        'dubai_municipality_registration',

        // Brand Highlights
        'brand_usp',
        'top_products',

        // Supporting Documents
        'logo',
        'trademark_document',
        'relationship_proof',
        'purchase_proof',
        'self_declaration',
        'product_pictures',

        // Status and Activation
        'is_active',
        'status',
    ];

    protected $casts = [
        // Boolean fields
        'is_trademark_registered' => 'boolean',
        'manufacturer' => 'boolean',
        'brand_owner' => 'boolean',
        'marketing_auth_holder' => 'boolean',
        'exclusive_distributor' => 'boolean',
        'authorized_distributor' => 'boolean',
        'wholesaler' => 'boolean',
        'authorized_retailer' => 'boolean',
        'direct_importer' => 'boolean',
        'parallel_importer' => 'boolean',
        'drop_shipper' => 'boolean',
        'is_active' => 'boolean',

        // JSON fields
        'top_products' => 'array',
        'product_pictures' => 'array',

        // Integer fields
        'skus_on_brand_website' => 'integer',
        'skus_on_amazon' => 'integer',
        'skus_on_noon' => 'integer',
        'skus_on_other_marketplaces' => 'integer',
        'skus_on_own_website' => 'integer',
    ];

    public function products()
    {
        return $this->hasMany(Product::class);
    }
}
