<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class SupportTicket extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'vendor_id',
        'order_id',
        'reason_id',
        'category_id',
        'topic_id',
        'subject',
        'message',
        'priority',
        'status',
        'record_status',
        'assigned_to',
        'resolved_at',
        'code',
    ];

    protected static function booted()
    {
        static::creating(function ($ticket) {
            $ticket->code = self::generateCode($ticket);
        });
    }


    public static function generateCode($ticket)
    {
        $prefix = 'CA';
        if ($ticket->reason_id) {
            $reason = SupportReason::find($ticket->reason_id);
            if ($reason) {
                $prefix = $reason->code_prefix;
            }
        }

        $date = now()->format('ymd');
        $sequence = str_pad((self::withTrashed()->max('id') + 1), 6, '0', STR_PAD_LEFT);

        return $prefix . $date . $sequence;
    }

    // Relationships

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function reason()
    {
        return $this->belongsTo(SupportReason::class);
    }

    public function category()
    {
        return $this->belongsTo(SupportCategory::class);
    }

    public function topic()
    {
        return $this->belongsTo(SupportTopic::class);
    }

    public function assignedTo()
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function messages()
    {
        return $this->hasMany(SupportTicketMessage::class);
    }
}
