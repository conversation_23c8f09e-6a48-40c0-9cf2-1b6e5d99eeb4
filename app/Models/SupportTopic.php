<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportTopic extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'category_id', 'name', 'status'
    ];

    public function category()
    {
        return $this->belongsTo(SupportCategory::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
