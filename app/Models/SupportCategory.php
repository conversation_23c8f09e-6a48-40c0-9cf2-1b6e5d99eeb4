<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportCategory extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'user_id', 'name', 'status'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function topics()
    {
        return $this->hasMany(SupportTopic::class, 'category_id');
    }
}
