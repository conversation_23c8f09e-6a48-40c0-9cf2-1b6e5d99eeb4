<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Customer extends Model
{
    protected $fillable = [
        'user_id',
        'gender',
        'date_of_birth',
        'loyalty_points',
        'customer_type',
        'preferred_language',
        'preferred_currency',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'loyalty_points' => 'integer',
    ];
    public function user()
    {
        return $this->belongsTo(User::class);
    }
  


}
